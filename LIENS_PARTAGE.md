# 🚀 PioneerTech - Liens de Partage

## 📱 Lien Principal d'Installation
```
http://localhost/setup.php
```

## 📧 Messages Prêts à Envoyer

### Pour WhatsApp :
```
🚀 Découvrez PioneerTech - Application de gestion d'entreprise complète !

✅ Gestion des clients
✅ Devis et factures automatiques  
✅ Suivi des projets et tâches
✅ Interface moderne et responsive

Installation en 1 clic : http://localhost/setup.php

#GestionEntreprise #PioneerTech #ApplicationWeb
```

### Pour Email :
**Sujet :** PioneerTech - Application de Gestion d'Entreprise

**Corps du message :**
```
Bon<PERSON>r,

Je vous partage PioneerTech, une application de gestion d'entreprise complète et moderne.

🚀 Installation en 1 clic : http://localhost/setup.php

Caractéristiques principales :
✅ Gestion complète des clients
✅ Création de devis et factures
✅ Suivi des projets et tâches
✅ Dashboard avec statistiques
✅ Interface responsive (mobile/desktop)
✅ Installation automatique en 30 secondes

Accès par défaut :
- Utilisateur : admin
- Mot de passe : admin123

L'installation est entièrement automatique et ne nécessite aucune configuration technique.

Cordialement
```

### Pour SMS :
```
🚀 PioneerTech - App de gestion d'entreprise complète !
Installation 1-clic : http://localhost/setup.php
Gestion clients, devis, factures, projets. Prêt en 30s !
```

## 🔗 Liens Directs

| Type | Lien |
|------|------|
| **Page d'accueil** | `http://localhost/setup.php` |
| **Installation directe** | `http://localhost/install_pioneertech.php` |
| **Application** | `http://localhost/pioneertech/` |
| **phpMyAdmin** | `http://localhost/phpmyadmin` |

## 📱 QR Code
Un QR Code est automatiquement généré sur la page `http://localhost/setup.php` pour un partage facile.

## 🌐 Pour Partage Public
Si vous voulez partager publiquement, vous pouvez :

1. **Utiliser ngrok** (pour rendre localhost accessible) :
   ```bash
   ngrok http 80
   ```

2. **Héberger sur un serveur web** :
   - Copier le dossier `pioneertech` sur votre hébergement
   - Remplacer `localhost` par votre nom de domaine

3. **Utiliser un raccourcisseur d'URL** :
   - bit.ly
   - tinyurl.com
   - t.ly

## 📋 Instructions d'Installation pour les Destinataires

1. Cliquer sur le lien : `http://localhost/setup.php`
2. Cliquer sur "Installer PioneerTech Maintenant"
3. Attendre la fin de l'installation (30 secondes)
4. Se connecter avec : `admin` / `admin123`

## 🔧 Prérequis Techniques
- Serveur web (Apache/Nginx)
- PHP 7.4+
- MySQL 5.7+
- XAMPP recommandé pour Windows

## 📞 Support
En cas de problème, les destinataires peuvent :
- Vérifier que XAMPP est démarré
- Redémarrer Apache et MySQL
- Contacter le support technique

---
*PioneerTech - Application de Gestion d'Entreprise v1.0*
