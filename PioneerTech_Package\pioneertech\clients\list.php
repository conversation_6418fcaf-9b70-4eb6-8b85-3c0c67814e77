<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once '../db/functions.php';
$clients = getAllClients();
$page_title = 'Clients';
include '../templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../templates/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h3 mb-2">Liste des Clients</h1>
            
            <div class="card">
                <div class="card-header">
                    <a href="add.php" class="btn btn-primary">Ajouter un client</a>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Email</th>
                                <th>Téléphone</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($clients as $client): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($client['nom']); ?></td>
                                <td><?php echo htmlspecialchars($client['email']); ?></td>
                                <td><?php echo htmlspecialchars($client['telephone']); ?></td>
                                <td>
                                    <a href="view.php?id=<?php echo $client['id']; ?>" class="btn btn-info btn-sm">Voir</a>
                                    <a href="edit.php?id=<?php echo $client['id']; ?>" class="btn btn-warning btn-sm">Modifier</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../templates/footer.php'; ?>