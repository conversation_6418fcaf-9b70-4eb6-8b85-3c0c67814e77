# 📖 Guide d'Installation PioneerTech

## 🎯 Prérequis
- Serveur web (Apache/Nginx)
- PHP 7.4 ou supérieur
- MySQL 5.7 ou supérieur
- XAMPP recommandé pour Windows

## 🚀 Installation Automatique (Recommandée)

### Étape 1 : Préparation
1. Téléchargez et décompressez le package PioneerTech
2. <PERSON><PERSON><PERSON><PERSON> XAMPP (Apache + MySQL)
3. <PERSON><PERSON><PERSON> le dossier `pioneertech` dans `C:/xampp/htdocs/`

### Étape 2 : Installation
1. Ouvrez votre navigateur
2. <PERSON><PERSON> sur `http://localhost/pioneertech/install_auto.php`
3. Cliquez sur "Lancer l'installation"
4. Attendez la fin de l'installation (30 secondes)

### Étape 3 : Première Connexion
1. Allez sur `http://localhost/pioneertech/`
2. Connectez-vous avec :
   - Utilisateur : `admin`
   - Mot de passe : `admin123`

## 🔧 Installation Manuelle

### Étape 1 : Base de Données
1. Ouvrez phpMyAdmin (`http://localhost/phpmyadmin`)
2. Créez une nouvelle base de données nommée `pioneertech`
3. Importez le fichier `database.sql`

### Étape 2 : Configuration
1. Ouvrez le fichier `pioneertech/config.php`
2. Modifiez les paramètres de connexion si nécessaire :
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'pioneertech');
   define('DB_USER', 'root');
   define('DB_PASS', '');
   ```

### Étape 3 : Test
1. Accédez à `http://localhost/pioneertech/`
2. Connectez-vous avec admin/admin123

## 🐛 Résolution des Problèmes

### Erreur de Connexion à la Base de Données
- Vérifiez que MySQL est démarré dans XAMPP
- Vérifiez les paramètres dans `config.php`
- Assurez-vous que la base `pioneertech` existe

### Page Blanche
- Activez l'affichage des erreurs PHP
- Vérifiez les logs d'erreur Apache
- Vérifiez les permissions des fichiers

### Erreur 404
- Vérifiez que le dossier est dans `htdocs`
- Vérifiez l'URL : `http://localhost/pioneertech/`
- Redémarrez Apache

## 📱 Utilisation Mobile
L'application est responsive et fonctionne sur :
- Smartphones
- Tablettes  
- Ordinateurs de bureau

## 🔒 Sécurité
Après installation :
1. Changez le mot de passe admin
2. Configurez des sauvegardes régulières
3. Mettez à jour PHP/MySQL régulièrement

## 📊 Fonctionnalités Avancées
- Export PDF des devis/factures
- Notifications par email
- Rapports statistiques
- Sauvegarde automatique

---
*Support technique : Consultez README.md*