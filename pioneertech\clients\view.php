<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once '../db/connection.php';
$page_title = "Détails du client";
include '../templates/header.php';

$id = $_GET['id'] ?? 0;
$stmt = $pdo->prepare("SELECT * FROM clients WHERE id = ?");
$stmt->execute([$id]);
$client = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$client) {
    header("Location: list.php");
    exit();
}
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../templates/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Détails du client</h1>

            <div class="card">
                <div class="card-header">
                    <h5><?php echo htmlspecialchars($client['nom']); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Email:</strong> <?php echo htmlspecialchars($client['email']); ?></p>
                            <p><strong>Téléphone:</strong> <?php echo htmlspecialchars($client['telephone']); ?></p>
                            <p><strong>Entreprise:</strong> <?php echo htmlspecialchars($client['entreprise']); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Adresse:</strong><br><?php echo nl2br(htmlspecialchars($client['adresse'])); ?></p>
                            <p><strong>Date de création:</strong> <?php echo date('d/m/Y H:i', strtotime($client['date_creation'])); ?></p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="edit.php?id=<?php echo $client['id']; ?>" class="btn btn-warning">Modifier</a>
                        <a href="list.php" class="btn btn-secondary">Retour à la liste</a>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../templates/footer.php'; ?>