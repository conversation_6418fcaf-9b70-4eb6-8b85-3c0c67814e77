-- Script de création de la base de données PioneerTech
CREATE DATABASE IF NOT EXISTS pioneertech CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE pioneertech;

-- Table des clients
CREATE TABLE IF NOT EXISTS clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    telephone VARCHAR(20),
    entreprise VARCHAR(100),
    adresse TEXT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des services
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    description TEXT,
    prix_base DECIMAL(10,2) NOT NULL,
    duree_estimee INT, -- en heures
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des devis
CREATE TABLE IF NOT EXISTS devis (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    montant_total DECIMAL(10,2) NOT NULL,
    description TEXT,
    statut ENUM("en_attente", "accepte", "refuse") DEFAULT "en_attente",
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
);

-- Table des factures
CREATE TABLE IF NOT EXISTS factures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    devis_id INT,
    montant_total DECIMAL(10,2) NOT NULL,
    montant_paye DECIMAL(10,2) DEFAULT 0,
    statut ENUM("en_attente", "payee", "en_retard") DEFAULT "en_attente",
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_echeance DATE,
    date_paiement DATE,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (devis_id) REFERENCES devis(id) ON DELETE SET NULL
);

-- Table des tâches
CREATE TABLE IF NOT EXISTS taches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(200) NOT NULL,
    description TEXT,
    priorite ENUM("basse", "moyenne", "haute") DEFAULT "moyenne",
    statut ENUM("nouvelle", "en_cours", "terminee", "annulee") DEFAULT "nouvelle",
    assignee_id INT,
    client_id INT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_echeance DATE,
    date_completion TIMESTAMP NULL,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL
);

-- Table des contrats
CREATE TABLE IF NOT EXISTS contrats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    titre VARCHAR(200) NOT NULL,
    description TEXT,
    montant DECIMAL(10,2) NOT NULL,
    date_debut DATE NOT NULL,
    date_fin DATE NOT NULL,
    statut ENUM("actif", "expire", "resilie") DEFAULT "actif",
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
);

-- Table des employés
CREATE TABLE IF NOT EXISTS employes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    telephone VARCHAR(20),
    poste VARCHAR(100),
    salaire DECIMAL(10,2),
    date_embauche DATE,
    statut ENUM("actif", "inactif", "conge") DEFAULT "actif",
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des stagiaires
CREATE TABLE IF NOT EXISTS stagiaires (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    telephone VARCHAR(20),
    ecole VARCHAR(200),
    niveau_etude VARCHAR(100),
    date_debut DATE NOT NULL,
    date_fin DATE NOT NULL,
    tuteur_id INT,
    statut ENUM("actif", "termine", "abandonne") DEFAULT "actif",
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tuteur_id) REFERENCES employes(id) ON DELETE SET NULL
);

-- Insertion de données de test
INSERT IGNORE INTO clients (nom, email, telephone, entreprise, adresse) VALUES
("Entreprise ABC", "<EMAIL>", "0123456789", "ABC Corp", "123 Rue de la Paix, Paris"),
("Jean Dupont", "<EMAIL>", "0987654321", "Freelance", "456 Avenue des Champs, Lyon"),
("Marie Martin", "<EMAIL>", "0147258369", "Martin SARL", "789 Boulevard Saint-Germain, Marseille");

INSERT IGNORE INTO services (nom, description, prix_base, duree_estimee) VALUES
("Développement Web", "Création de sites web sur mesure", 500.00, 40),
("Maintenance", "Maintenance et support technique", 80.00, 2),
("Formation", "Formation aux outils numériques", 200.00, 8),
("Consultation", "Conseil en stratégie digitale", 150.00, 4);

INSERT IGNORE INTO taches (titre, description, priorite, statut) VALUES
("Finaliser le projet ABC", "Terminer le développement du site web pour ABC Corp", "haute", "en_cours"),
("Maintenance serveur", "Vérifier et mettre à jour les serveurs", "moyenne", "nouvelle"),
("Formation client", "Former l'équipe de Jean Dupont aux nouveaux outils", "basse", "nouvelle");