# 🚀 PioneerTech v2.0 - Prêt pour l'Hébergement

## 📋 Instructions d'Installation

### 1. Préparation
- Uploadez tous les fichiers de ce dossier sur votre hébergeur
- Placez-les dans le dossier `public_html` ou `www`

### 2. Configuration de la Base de Données
1. Créez une base de données MySQL sur votre hébergeur
2. Notez les informations de connexion :
   - Host (généralement `localhost`)
   - Nom de la base de données
   - Nom d'utilisateur
   - Mot de passe

### 3. Configuration de l'Application
1. Ouvrez le fichier `config_hosting.php`
2. Modifiez les paramètres de base de données
3. Changez l'URL de l'application
4. Renommez `config_hosting.php` en `config.php`

### 4. Installation
1. Allez sur `https://votre-domaine.com/install.php`
2. Suivez les instructions d'installation
3. Supprim<PERSON> le fichier `install.php` après installation

### 5. Sécurité
- Changez le mot de passe admin par défaut
- Configurez SSL/HTTPS si disponible
- Testez toutes les fonctionnalités

## 🔧 Hébergeurs Recommandés

### Gratuits :
- 000webhost.com
- infinityfree.net
- freehostia.com

### Payants :
- OVH (français)
- Hostinger
- SiteGround

## 📞 Support
En cas de problème, vérifiez :
1. Les paramètres de base de données
2. Les permissions des fichiers (755)
3. La version PHP (7.4+ requis)
4. Les extensions PHP (PDO, MySQL)

## 🎉 Félicitations !
Votre PioneerTech sera bientôt en ligne !