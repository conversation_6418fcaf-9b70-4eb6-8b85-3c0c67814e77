<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PioneerTech - Installation Rapide</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .hero-card { 
            background: white; 
            border-radius: 20px; 
            box-shadow: 0 15px 35px rgba(0,0,0,0.3); 
            overflow: hidden;
        }
        .hero-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
        }
        .feature-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            border-radius: 15px;
        }
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .btn-install {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            padding: 1rem 2rem;
            font-size: 1.2rem;
            border-radius: 50px;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
            transition: all 0.3s ease;
        }
        .btn-install:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.6);
        }
        .share-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
        }
        .share-btn {
            margin: 0.5rem;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .share-btn:hover {
            transform: translateY(-2px);
            text-decoration: none;
        }
        .whatsapp { background: #25D366; color: white; }
        .email { background: #0078D4; color: white; }
        .copy-link { background: #6c757d; color: white; }
        
        @media (max-width: 768px) {
            .hero-header { padding: 2rem 1rem; }
            .btn-install { font-size: 1rem; padding: 0.8rem 1.5rem; }
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="hero-card">
                    <div class="hero-header">
                        <h1 class="display-3 mb-3">
                            <i class="fas fa-rocket"></i> PioneerTech
                        </h1>
                        <p class="lead mb-4">Application de Gestion d'Entreprise Complète</p>
                        <p class="mb-0">Installation en 1 clic - Prêt en 30 secondes</p>
                    </div>
                    
                    <div class="p-5">
                        <div class="text-center mb-5">
                            <h2 class="h3 mb-4">🚀 Installation Ultra-Rapide</h2>
                            <p class="text-muted mb-4">Cliquez sur le bouton ci-dessous pour installer automatiquement PioneerTech avec tous ses modules</p>
                            
                            <a href="install_pioneertech.php" class="btn btn-success btn-install">
                                <i class="fas fa-download"></i> Installer PioneerTech Maintenant
                            </a>
                            
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-clock"></i> Installation automatique en moins de 30 secondes
                                </small>
                            </div>
                        </div>

                        <div class="row mb-5">
                            <div class="col-md-4 mb-3">
                                <div class="card feature-card h-100 text-center p-3">
                                    <div class="card-body">
                                        <i class="fas fa-users fa-3x text-primary mb-3"></i>
                                        <h5>Gestion Clients</h5>
                                        <p class="small text-muted">Base de données complète des clients avec historique</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card feature-card h-100 text-center p-3">
                                    <div class="card-body">
                                        <i class="fas fa-file-invoice-dollar fa-3x text-success mb-3"></i>
                                        <h5>Devis & Factures</h5>
                                        <p class="small text-muted">Création automatique de devis et facturation</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card feature-card h-100 text-center p-3">
                                    <div class="card-body">
                                        <i class="fas fa-tasks fa-3x text-info mb-3"></i>
                                        <h5>Suivi Projets</h5>
                                        <p class="small text-muted">Gestion des tâches et suivi des projets</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="share-section">
                            <h4 class="text-center mb-4">
                                <i class="fas fa-share-alt"></i> Partager ce lien d'installation
                            </h4>
                            
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="input-group mb-3">
                                        <input type="text" class="form-control" id="installLink" 
                                               value="<?php echo 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/setup.php'; ?>" 
                                               readonly>
                                        <button class="btn btn-outline-secondary" type="button" onclick="copyLink()">
                                            <i class="fas fa-copy"></i> Copier
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div id="qrcode" class="d-inline-block"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <h6 class="mb-3">Partager via :</h6>
                                
                                <a href="https://wa.me/?text=🚀 Découvrez PioneerTech - Application de gestion d'entreprise complète ! Installation en 1 clic : <?php echo urlencode('http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/setup.php'); ?>" 
                                   target="_blank" class="share-btn whatsapp">
                                    <i class="fab fa-whatsapp"></i> WhatsApp
                                </a>
                                
                                <a href="mailto:?subject=PioneerTech - Application de Gestion&body=Bonjour,%0A%0AJe vous partage PioneerTech, une application de gestion d'entreprise complète.%0A%0A🚀 Installation en 1 clic : <?php echo urlencode('http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/setup.php'); ?>%0A%0ACaractéristiques :%0A✅ Gestion des clients%0A✅ Devis et factures%0A✅ Suivi des projets%0A✅ Interface moderne%0A%0ACordialement" 
                                   class="share-btn email">
                                    <i class="fas fa-envelope"></i> Email
                                </a>
                                
                                <button onclick="copyLink()" class="share-btn copy-link">
                                    <i class="fas fa-link"></i> Copier le lien
                                </button>
                            </div>
                        </div>

                        <div class="row mt-5">
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <i class="fas fa-info-circle"></i> Informations Techniques
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled mb-0">
                                            <li><i class="fas fa-check text-success"></i> PHP + MySQL</li>
                                            <li><i class="fas fa-check text-success"></i> Bootstrap 5</li>
                                            <li><i class="fas fa-check text-success"></i> Responsive Design</li>
                                            <li><i class="fas fa-check text-success"></i> Installation automatique</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <i class="fas fa-shield-alt"></i> Accès par Défaut
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-2"><strong>Utilisateur :</strong> <code>admin</code></p>
                                        <p class="mb-2"><strong>Mot de passe :</strong> <code>admin123</code></p>
                                        <p class="mb-0"><small class="text-muted">Modifiable après installation</small></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    
    <script>
        // Générer le QR Code
        const qrCodeUrl = window.location.href;
        QRCode.toCanvas(document.getElementById('qrcode'), qrCodeUrl, {
            width: 120,
            height: 120,
            colorDark: '#667eea',
            colorLight: '#ffffff'
        });

        // Fonction pour copier le lien
        function copyLink() {
            const linkInput = document.getElementById('installLink');
            linkInput.select();
            linkInput.setSelectionRange(0, 99999);
            
            try {
                document.execCommand('copy');
                
                // Feedback visuel
                const button = event.target.closest('button');
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> Copié !';
                button.classList.add('btn-success');
                
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('btn-success');
                }, 2000);
                
            } catch (err) {
                alert('Erreur lors de la copie. Veuillez copier manuellement le lien.');
            }
        }

        // Animation au scroll
        window.addEventListener('scroll', () => {
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach(card => {
                const rect = card.getBoundingClientRect();
                if (rect.top < window.innerHeight && rect.bottom > 0) {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }
            });
        });
    </script>
</body>
</html>
