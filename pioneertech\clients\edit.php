<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once '../db/connection.php';
$page_title = "Modifier le client";
include '../templates/header.php';

$id = $_GET['id'] ?? 0;
$stmt = $pdo->prepare("SELECT * FROM clients WHERE id = ?");
$stmt->execute([$id]);
$client = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$client) {
    header("Location: list.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nom = $_POST['nom'];
    $email = $_POST['email'];
    $telephone = $_POST['telephone'];
    $entreprise = $_POST['entreprise'];
    $adresse = $_POST['adresse'];

    $sql = "UPDATE clients SET nom = ?, email = ?, telephone = ?, entreprise = ?, adresse = ? WHERE id = ?";
    $stmt = $pdo->prepare($sql);

    if ($stmt->execute([$nom, $email, $telephone, $entreprise, $adresse, $id])) {
        $success = "Client modifié avec succès.";
        // Recharger les données
        $stmt = $pdo->prepare("SELECT * FROM clients WHERE id = ?");
        $stmt->execute([$id]);
        $client = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        $error = "Erreur lors de la modification du client.";
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../templates/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Modifier le client</h1>

            <?php if (isset($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">Nom</label>
                    <input type="text" name="nom" class="form-control" value="<?php echo htmlspecialchars($client['nom']); ?>" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($client['email']); ?>" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Téléphone</label>
                    <input type="text" name="telephone" class="form-control" value="<?php echo htmlspecialchars($client['telephone']); ?>">
                </div>
                <div class="mb-3">
                    <label class="form-label">Entreprise</label>
                    <input type="text" name="entreprise" class="form-control" value="<?php echo htmlspecialchars($client['entreprise']); ?>">
                </div>
                <div class="mb-3">
                    <label class="form-label">Adresse</label>
                    <textarea name="adresse" class="form-control" rows="3"><?php echo htmlspecialchars($client['adresse']); ?></textarea>
                </div>
                <button type="submit" class="btn btn-primary">Modifier</button>
                <a href="view.php?id=<?php echo $client['id']; ?>" class="btn btn-info">Voir</a>
                <a href="list.php" class="btn btn-secondary">Retour</a>
            </form>
        </main>
    </div>
</div>

<?php include '../templates/footer.php'; ?>