<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once '../db/connection.php';
$page_title = "Dashboard";
include '../templates/header.php';

$stats = [
    'clients' => $pdo->query("SELECT COUNT(*) FROM clients")->fetchColumn(),
    'services' => $pdo->query("SELECT COUNT(*) FROM services")->fetchColumn(),
    'devis' => $pdo->query("SELECT COUNT(*) FROM devis")->fetchColumn(),
    'factures' => $pdo->query("SELECT COUNT(*) FROM factures")->fetchColumn()
];
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../templates/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Dashboard PioneerTech</h1>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="card text-white bg-primary mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Clients</h5>
                            <p class="card-text"><?php echo $stats['clients']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Services</h5>
                            <p class="card-text"><?php echo $stats['services']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Devis</h5>
                            <p class="card-text"><?php echo $stats['devis']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Factures</h5>
                            <p class="card-text"><?php echo $stats['factures']; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../templates/footer.php'; ?>