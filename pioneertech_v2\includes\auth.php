<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/database.php';

function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
}

function requireAuth($allowedRoles = []) {
    if (!isLoggedIn()) {
        header("Location: " . APP_URL . "auth/login.php");
        exit();
    }
    
    if (!empty($allowedRoles) && !in_array($_SESSION['user_role'], $allowedRoles)) {
        header("Location: " . APP_URL . "auth/unauthorized.php");
        exit();
    }
}

function hasPermission($permission) {
    if (!isLoggedIn()) return false;
    
    $permissions = getUserPermissions($_SESSION['user_id']);
    return in_array($permission, $permissions);
}

function getUserPermissions($userId) {
    global $pdo;
    $stmt = $pdo->prepare("
        SELECT p.name 
        FROM permissions p 
        JOIN role_permissions rp ON p.id = rp.permission_id 
        JOIN users u ON u.role_id = rp.role_id 
        WHERE u.id = ?
    ");
    $stmt->execute([$userId]);
    return $stmt->fetchAll(PDO::FETCH_COLUMN);
}

function login($email, $password) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT u.*, r.name as role_name 
        FROM users u 
        JOIN roles r ON u.role_id = r.id 
        WHERE u.email = ? AND u.status = 'active'
    ");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_role'] = $user['role_name'];
        $_SESSION['user_avatar'] = $user['avatar'];
        
        // Log de connexion
        logActivity($user['id'], 'login', 'Connexion utilisateur');
        
        return true;
    }
    
    return false;
}

function logout() {
    if (isLoggedIn()) {
        logActivity($_SESSION['user_id'], 'logout', 'Déconnexion utilisateur');
    }
    
    session_destroy();
    header("Location: " . APP_URL . "auth/login.php");
    exit();
}

function logActivity($userId, $action, $description) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        INSERT INTO activity_logs (user_id, action, description, ip_address, created_at) 
        VALUES (?, ?, ?, ?, NOW())
    ");
    $stmt->execute([$userId, $action, $description, $_SERVER['REMOTE_ADDR']]);
}
?>