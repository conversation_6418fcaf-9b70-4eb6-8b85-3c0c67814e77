<?php
require_once "config.php";
require_once "includes/auth.php";

// Redirection selon le rôle de l'utilisateur
if (isLoggedIn()) {
    $role = $_SESSION['user_role'];
    switch ($role) {
        case ROLE_ADMIN:
            header("Location: admin/dashboard.php");
            break;
        case ROLE_EMPLOYEE:
            header("Location: employee/dashboard.php");
            break;
        case ROLE_INTERN:
            header("Location: intern/dashboard.php");
            break;
        case ROLE_CLIENT:
            header("Location: client/dashboard.php");
            break;
        default:
            header("Location: auth/login.php");
    }
} else {
    header("Location: auth/login.php");
}
exit();
?>