<?php
// Script pour créer un ZIP du package PioneerTech
$zip = new ZipArchive();
$zipname = "PioneerTech_v1.0_" . date("Y-m-d") . ".zip";

if ($zip->open($zipname, ZipArchive::CREATE) !== TRUE) {
    exit("Impossible de créer le fichier ZIP\n");
}

function addFolderToZip($folder, $zipFile, $exclusiveLength) {
    $handle = opendir($folder);
    while (false !== $f = readdir($handle)) {
        if ($f != "." && $f != "..") {
            $filePath = "$folder/$f";
            $localPath = substr($filePath, $exclusiveLength);
            if (is_file($filePath)) {
                $zipFile->addFile($filePath, $localPath);
            } elseif (is_dir($filePath)) {
                $zipFile->addEmptyDir($localPath);
                addFolderToZip($filePath, $zipFile, $exclusiveLength);
            }
        }
    }
    closedir($handle);
}

addFolderToZip(__DIR__, $zip, strlen(__DIR__) + 1);
$zip->close();

echo "✅ Package créé : $zipname\n";
echo "📦 Taille : " . round(filesize($zipname) / 1024 / 1024, 2) . " MB\n";
echo "🚀 Prêt pour le partage !\n";
?>