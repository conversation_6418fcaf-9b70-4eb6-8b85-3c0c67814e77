<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once '../db/connection.php';
$page_title = "Nouveau devis";
include '../templates/header.php';

// Récupérer les clients
$clients_stmt = $pdo->query("SELECT id, nom FROM clients ORDER BY nom");
$clients = $clients_stmt->fetchAll(PDO::FETCH_ASSOC);

// Récupérer les services
$services_stmt = $pdo->query("SELECT id, nom, prix_base FROM services ORDER BY nom");
$services = $services_stmt->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $client_id = $_POST['client_id'];
    $numero = "DEV-" . date("Y") . "-" . str_pad(rand(1, 9999), 4, "0", STR_PAD_LEFT);
    $montant_total = $_POST['montant_total'];
    $description = $_POST['description'];

    $sql = "INSERT INTO devis (numero, client_id, montant_total, description, statut, date_creation) VALUES (?, ?, ?, ?, 'en_attente', NOW())";
    $stmt = $pdo->prepare($sql);

    if ($stmt->execute([$numero, $client_id, $montant_total, $description])) {
        $success = "Devis créé avec succès.";
    } else {
        $error = "Erreur lors de la création du devis.";
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../templates/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Nouveau devis</h1>

            <?php if (isset($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">Client</label>
                    <select name="client_id" class="form-control" required>
                        <option value="">Sélectionner un client</option>
                        <?php foreach ($clients as $client): ?>
                            <option value="<?php echo $client['id']; ?>"><?php echo htmlspecialchars($client['nom']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Description</label>
                    <textarea name="description" class="form-control" rows="4" required></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Montant total (€)</label>
                    <input type="number" name="montant_total" class="form-control" step="0.01" required>
                </div>
                <button type="submit" class="btn btn-primary">Créer le devis</button>
                <a href="list.php" class="btn btn-secondary">Retour</a>
            </form>
        </main>
    </div>
</div>

<?php include '../templates/footer.php'; ?>