CREATE TABLE clients (
  id INT PRIMARY KEY AUTO_INCREMENT,
  nom VARCHAR(100) NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  telephone VARCHAR(20),
  entreprise VARCHAR(100),
  adresse TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE services (
  id INT PRIMARY KEY AUTO_INCREMENT,
  nom VARCHAR(100) NOT NULL,
  categorie VARCHAR(50),
  description TEXT,
  prix_base DECIMAL(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE supplements (
  id INT PRIMARY KEY AUTO_INCREMENT,
  service_id INT NOT NULL,
  nom VARCHAR(100) NOT NULL,
  prix DECIMAL(10,2) NOT NULL,
  FOREIGN KEY (service_id) REFERENCES services(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE devis (
  id INT PRIMARY KEY AUTO_INCREMENT,
  client_id INT NOT NULL,
  statut ENUM('en_attente', 'accepte', 'refuse') DEFAULT 'en_attente',
  date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
  date_expiration DATE,
  total DECIMAL(10,2),
  FOREIGN KEY (client_id) REFERENCES clients(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE devis_services (
  devis_id INT NOT NULL,
  service_id INT NOT NULL,
  quantite INT DEFAULT 1,
  supplement_id INT,
  PRIMARY KEY (devis_id, service_id),
  FOREIGN KEY (devis_id) REFERENCES devis(id),
  FOREIGN KEY (service_id) REFERENCES services(id),
  FOREIGN KEY (supplement_id) REFERENCES supplements(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE factures (
  id INT PRIMARY KEY AUTO_INCREMENT,
  devis_id INT UNIQUE,
  numero VARCHAR(20) UNIQUE NOT NULL,
  date_emission DATE NOT NULL,
  echeance DATE NOT NULL,
  statut_paiement ENUM('paye', 'partiel', 'impaye') DEFAULT 'impaye',
  montant_total DECIMAL(10,2) NOT NULL,
  FOREIGN KEY (devis_id) REFERENCES devis(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE utilisateurs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  role ENUM('admin', 'employe', 'stagiaire') NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Index pour les recherches fréquentes
CREATE INDEX idx_clients_nom ON clients(nom);
CREATE INDEX idx_factures_numero ON factures(numero);
CREATE INDEX idx_devis_statut ON devis(statut);