<?php
// Script pour préparer PioneerTech pour l'hébergement web
echo "🌐 Préparation de PioneerTech pour l'hébergement web...\n\n";

$source_dir = __DIR__ . '/pioneertech_v2';
$hosting_dir = __DIR__ . '/pioneertech_hosting';

// Créer le dossier de préparation
if (!file_exists($hosting_dir)) {
    mkdir($hosting_dir, 0755, true);
}

// Fonction pour copier récursivement
function copyDirectory($src, $dst) {
    $dir = opendir($src);
    @mkdir($dst);
    while(false !== ( $file = readdir($dir)) ) {
        if (( $file != '.' ) && ( $file != '..' )) {
            if ( is_dir($src . '/' . $file) ) {
                copyDirectory($src . '/' . $file, $dst . '/' . $file);
            }
            else {
                copy($src . '/' . $file, $dst . '/' . $file);
            }
        }
    }
    closedir($dir);
}

// Copier tous les fichiers
echo "📁 Copie des fichiers...\n";
copyDirectory($source_dir, $hosting_dir);

// Créer un fichier de configuration pour l'hébergement
$hosting_config = '<?php
// Configuration pour hébergement web
// ⚠️ MODIFIEZ CES PARAMÈTRES SELON VOTRE HÉBERGEUR

// Configuration de la base de données
define(\'DB_HOST\', \'localhost\');                    // Généralement "localhost"
define(\'DB_NAME\', \'votre_nom_de_base\');           // Nom de votre base de données
define(\'DB_USER\', \'votre_nom_utilisateur\');       // Nom d\'utilisateur MySQL
define(\'DB_PASS\', \'votre_mot_de_passe\');          // Mot de passe MySQL

// Configuration de l\'application
define(\'APP_NAME\', \'PioneerTech v2.0\');
define(\'APP_VERSION\', \'2.0.0\');
define(\'APP_URL\', \'https://votre-domaine.com/\');   // ⚠️ CHANGEZ CETTE URL

// Configuration des uploads
define(\'UPLOAD_PATH\', __DIR__ . \'/uploads/\');
define(\'MAX_FILE_SIZE\', 10 * 1024 * 1024); // 10MB

// Configuration email (optionnel)
define(\'SMTP_HOST\', \'smtp.gmail.com\');
define(\'SMTP_PORT\', 587);
define(\'SMTP_USER\', \'<EMAIL>\');      // ⚠️ CHANGEZ CET EMAIL
define(\'SMTP_PASS\', \'votre-mot-de-passe-app\');     // ⚠️ CHANGEZ CE MOT DE PASSE

// Configuration des rôles
define(\'ROLE_ADMIN\', \'admin\');
define(\'ROLE_EMPLOYEE\', \'employee\');
define(\'ROLE_INTERN\', \'intern\');
define(\'ROLE_CLIENT\', \'client\');

// Configuration des langues
define(\'DEFAULT_LANG\', \'fr\');
define(\'SUPPORTED_LANGS\', [\'fr\', \'ar\', \'en\']);

// Configuration des devises
define(\'DEFAULT_CURRENCY\', \'EUR\');
define(\'TAX_RATE\', 0.20); // 20% TVA

// Gestion des erreurs (DÉSACTIVER EN PRODUCTION)
error_reporting(E_ALL);
ini_set(\'display_errors\', 0); // ⚠️ Mettre à 0 en production

// Timezone
date_default_timezone_set(\'Europe/Paris\');

// Démarrer la session
session_start();
?>';

file_put_contents($hosting_dir . '/config_hosting.php', $hosting_config);

// Créer un fichier .htaccess pour la sécurité
$htaccess = '# Sécurité et optimisation pour PioneerTech

# Bloquer l\'accès aux fichiers sensibles
<Files "config*.php">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files ".env">
    Order allow,deny
    Deny from all
</Files>

# Redirection HTTPS (décommentez si vous avez SSL)
# RewriteEngine On
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Compression des fichiers
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache des fichiers statiques
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Sécurité supplémentaire
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
</IfModule>

# Limiter la taille des uploads
LimitRequestBody 10485760

# Page d\'erreur personnalisée
ErrorDocument 404 /error404.php
ErrorDocument 500 /error500.php';

file_put_contents($hosting_dir . '/.htaccess', $htaccess);

// Créer un fichier README pour l'hébergement
$readme_hosting = '# 🚀 PioneerTech v2.0 - Prêt pour l\'Hébergement

## 📋 Instructions d\'Installation

### 1. Préparation
- Uploadez tous les fichiers de ce dossier sur votre hébergeur
- Placez-les dans le dossier `public_html` ou `www`

### 2. Configuration de la Base de Données
1. Créez une base de données MySQL sur votre hébergeur
2. Notez les informations de connexion :
   - Host (généralement `localhost`)
   - Nom de la base de données
   - Nom d\'utilisateur
   - Mot de passe

### 3. Configuration de l\'Application
1. Ouvrez le fichier `config_hosting.php`
2. Modifiez les paramètres de base de données
3. Changez l\'URL de l\'application
4. Renommez `config_hosting.php` en `config.php`

### 4. Installation
1. Allez sur `https://votre-domaine.com/install.php`
2. Suivez les instructions d\'installation
3. Supprimez le fichier `install.php` après installation

### 5. Sécurité
- Changez le mot de passe admin par défaut
- Configurez SSL/HTTPS si disponible
- Testez toutes les fonctionnalités

## 🔧 Hébergeurs Recommandés

### Gratuits :
- 000webhost.com
- infinityfree.net
- freehostia.com

### Payants :
- OVH (français)
- Hostinger
- SiteGround

## 📞 Support
En cas de problème, vérifiez :
1. Les paramètres de base de données
2. Les permissions des fichiers (755)
3. La version PHP (7.4+ requis)
4. Les extensions PHP (PDO, MySQL)

## 🎉 Félicitations !
Votre PioneerTech sera bientôt en ligne !';

file_put_contents($hosting_dir . '/README_HOSTING.md', $readme_hosting);

// Créer des pages d'erreur personnalisées
$error404 = '<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page non trouvée - PioneerTech</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 text-center">
                <h1 class="display-1 text-primary">404</h1>
                <h2>Page non trouvée</h2>
                <p class="lead">La page que vous cherchez n\'existe pas.</p>
                <a href="/" class="btn btn-primary">Retour à l\'accueil</a>
            </div>
        </div>
    </div>
</body>
</html>';

file_put_contents($hosting_dir . '/error404.php', $error404);

$error500 = '<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Erreur serveur - PioneerTech</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 text-center">
                <h1 class="display-1 text-danger">500</h1>
                <h2>Erreur serveur</h2>
                <p class="lead">Une erreur interne s\'est produite.</p>
                <a href="/" class="btn btn-primary">Retour à l\'accueil</a>
            </div>
        </div>
    </div>
</body>
</html>';

file_put_contents($hosting_dir . '/error500.php', $error500);

// Créer un fichier ZIP prêt pour l'upload
echo "📦 Création du package d\'hébergement...\n";

// Compresser le dossier
$zip = new ZipArchive();
$zipname = 'PioneerTech_v2_Ready_for_Hosting.zip';

if ($zip->open($zipname, ZipArchive::CREATE) !== TRUE) {
    exit("Impossible de créer le fichier ZIP\n");
}

function addFolderToZip($folder, $zipFile, $exclusiveLength) {
    $handle = opendir($folder);
    while (false !== $f = readdir($handle)) {
        if ($f != "." && $f != "..") {
            $filePath = "$folder/$f";
            $localPath = substr($filePath, $exclusiveLength);
            if (is_file($filePath)) {
                $zipFile->addFile($filePath, $localPath);
            } elseif (is_dir($filePath)) {
                $zipFile->addEmptyDir($localPath);
                addFolderToZip($filePath, $zipFile, $exclusiveLength);
            }
        }
    }
    closedir($handle);
}

addFolderToZip($hosting_dir, $zip, strlen($hosting_dir) + 1);
$zip->close();

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 PIONEERTECH PRÊT POUR L'HÉBERGEMENT ! 🎉\n";
echo str_repeat("=", 60) . "\n\n";

echo "📦 FICHIERS CRÉÉS :\n";
echo "   ✅ Dossier : $hosting_dir\n";
echo "   ✅ Package ZIP : $zipname\n";
echo "   ✅ Configuration d'hébergement\n";
echo "   ✅ Fichiers de sécurité (.htaccess)\n";
echo "   ✅ Pages d'erreur personnalisées\n";
echo "   ✅ Documentation complète\n\n";

echo "🚀 PROCHAINES ÉTAPES :\n";
echo "1. 📤 Uploadez le fichier ZIP sur votre hébergeur\n";
echo "2. 🗄️  Créez une base de données MySQL\n";
echo "3. ⚙️  Modifiez config_hosting.php avec vos paramètres\n";
echo "4. 🌐 Accédez à votre-domaine.com/install.php\n";
echo "5. 🎯 Lancez l'installation\n\n";

echo "📋 HÉBERGEURS RECOMMANDÉS :\n";
echo "   🆓 Gratuit : 000webhost.com\n";
echo "   💰 Payant : OVH.com (français)\n";
echo "   🌍 International : Hostinger.com\n\n";

echo "✨ Votre PioneerTech est prêt à conquérir le web ! ✨\n";
echo str_repeat("=", 60) . "\n";
?>
