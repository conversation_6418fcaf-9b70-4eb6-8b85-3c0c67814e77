<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation PioneerTech v2.0</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
        }
        .install-card { 
            background: white; 
            border-radius: 20px; 
            box-shadow: 0 15px 35px rgba(0,0,0,0.3); 
        }
        .progress-step {
            opacity: 0;
            animation: fadeInUp 0.6s ease forwards;
        }
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="install-card p-5">
                    <div class="text-center mb-5">
                        <h1 class="display-4 text-primary mb-3">
                            <i class="fas fa-rocket"></i> PioneerTech v2.0
                        </h1>
                        <p class="lead">Installation Complète - Application de Gestion</p>
                    </div>

<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['install'])) {
    echo '<div class="installation-progress">';
    echo '<h3 class="text-center mb-4"><i class="fas fa-cogs fa-spin"></i> Installation en cours...</h3>';
    
    $step = 1;
    
    try {
        // Configuration de la base de données
        $host = 'localhost';
        $dbname = 'pioneertech_v2';
        $username = 'root';
        $password = '';

        echo '<div class="progress-step alert alert-info" style="animation-delay: 0.1s"><i class="fas fa-info-circle"></i> <strong>Étape ' . $step++ . ':</strong> Connexion au serveur MySQL...</div>';
        flush();
        
        // Créer la connexion pour créer la base de données
        $pdo_create = new PDO("mysql:host=$host", $username, $password);
        $pdo_create->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 0.3s"><i class="fas fa-check"></i> <strong>Étape ' . $step++ . ':</strong> Connexion MySQL établie!</div>';
        flush();
        
        // Créer la base de données
        echo '<div class="progress-step alert alert-info" style="animation-delay: 0.5s"><i class="fas fa-database"></i> <strong>Étape ' . $step++ . ':</strong> Création de la base de données...</div>';
        flush();
        
        $pdo_create->exec("CREATE DATABASE IF NOT EXISTS $dbname CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 0.7s"><i class="fas fa-check-circle"></i> <strong>Étape ' . $step++ . ':</strong> Base de données créée!</div>';
        flush();
        
        // Se connecter à la base de données créée
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo '<div class="progress-step alert alert-info" style="animation-delay: 0.9s"><i class="fas fa-table"></i> <strong>Étape ' . $step++ . ':</strong> Création des tables...</div>';
        flush();
        
        // Script SQL complet intégré
        $sql_queries = [
            // Table des rôles
            "CREATE TABLE IF NOT EXISTS roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) UNIQUE NOT NULL,
                display_name VARCHAR(100) NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
            
            // Table des utilisateurs
            "CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role_id INT NOT NULL DEFAULT 1,
                avatar VARCHAR(255),
                phone VARCHAR(20),
                address TEXT,
                status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
            
            // Table des notifications
            "CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
                action_url VARCHAR(500),
                is_read BOOLEAN DEFAULT FALSE,
                read_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
            
            // Table des clients
            "CREATE TABLE IF NOT EXISTS clients (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nom VARCHAR(100) NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                telephone VARCHAR(20),
                entreprise VARCHAR(100),
                adresse TEXT,
                ville VARCHAR(100),
                code_postal VARCHAR(10),
                pays VARCHAR(100) DEFAULT 'France',
                secteur_activite VARCHAR(100),
                status ENUM('actif', 'inactif', 'prospect') DEFAULT 'prospect',
                assigned_to INT,
                created_by INT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
            
            // Table des services
            "CREATE TABLE IF NOT EXISTS services (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nom VARCHAR(100) NOT NULL,
                description TEXT,
                prix_base DECIMAL(10,2) NOT NULL,
                duree_estimee INT,
                unite_duree ENUM('heures', 'jours', 'semaines', 'mois') DEFAULT 'heures',
                categorie VARCHAR(100),
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
            
            // Table des devis
            "CREATE TABLE IF NOT EXISTS devis (
                id INT AUTO_INCREMENT PRIMARY KEY,
                numero VARCHAR(50) UNIQUE NOT NULL,
                client_id INT NOT NULL,
                montant_ht DECIMAL(10,2) NOT NULL,
                taux_tva DECIMAL(5,2) DEFAULT 20.00,
                montant_tva DECIMAL(10,2) NOT NULL,
                montant_ttc DECIMAL(10,2) NOT NULL,
                remise DECIMAL(10,2) DEFAULT 0,
                description TEXT,
                statut ENUM('brouillon', 'envoye', 'accepte', 'refuse', 'expire') DEFAULT 'brouillon',
                date_envoi TIMESTAMP NULL,
                date_acceptation TIMESTAMP NULL,
                created_by INT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
            
            // Table des factures
            "CREATE TABLE IF NOT EXISTS factures (
                id INT AUTO_INCREMENT PRIMARY KEY,
                numero VARCHAR(50) UNIQUE NOT NULL,
                client_id INT NOT NULL,
                devis_id INT,
                montant_ht DECIMAL(10,2) NOT NULL,
                taux_tva DECIMAL(5,2) DEFAULT 20.00,
                montant_tva DECIMAL(10,2) NOT NULL,
                montant_ttc DECIMAL(10,2) NOT NULL,
                montant_paye DECIMAL(10,2) DEFAULT 0,
                statut ENUM('brouillon', 'envoyee', 'payee', 'en_retard', 'annulee') DEFAULT 'brouillon',
                mode_paiement ENUM('virement', 'cheque', 'especes', 'carte', 'paypal') NULL,
                date_envoi TIMESTAMP NULL,
                date_echeance DATE,
                date_paiement TIMESTAMP NULL,
                created_by INT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
            
            // Table des tâches
            "CREATE TABLE IF NOT EXISTS taches (
                id INT AUTO_INCREMENT PRIMARY KEY,
                titre VARCHAR(200) NOT NULL,
                description TEXT,
                priorite ENUM('basse', 'normale', 'haute', 'urgente') DEFAULT 'normale',
                statut ENUM('nouvelle', 'en_cours', 'en_attente', 'terminee', 'annulee') DEFAULT 'nouvelle',
                progression INT DEFAULT 0,
                client_id INT,
                assigned_to INT,
                created_by INT,
                estimated_hours DECIMAL(5,2),
                actual_hours DECIMAL(5,2) DEFAULT 0,
                date_debut DATE,
                date_echeance DATE,
                date_completion TIMESTAMP NULL,
                notified_overdue BOOLEAN DEFAULT FALSE,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
            
            // Table des paramètres
            "CREATE TABLE IF NOT EXISTS app_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                category VARCHAR(50) DEFAULT 'general',
                description TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )"
        ];
        
        $table_count = 0;
        foreach ($sql_queries as $query) {
            $pdo->exec($query);
            $table_count++;
        }
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 1.3s"><i class="fas fa-check-double"></i> <strong>Étape ' . $step++ . ':</strong> ' . $table_count . ' tables créées!</div>';
        flush();
        
        // Insérer les données initiales
        echo '<div class="progress-step alert alert-info" style="animation-delay: 1.5s"><i class="fas fa-users"></i> <strong>Étape ' . $step++ . ':</strong> Insertion des données...</div>';
        flush();
        
        // Rôles par défaut
        $pdo->exec("INSERT IGNORE INTO roles (name, display_name, description) VALUES
            ('admin', 'Administrateur', 'Accès complet à toutes les fonctionnalités'),
            ('employee', 'Employé', 'Accès aux fonctionnalités de travail quotidien'),
            ('intern', 'Stagiaire', 'Accès limité pour les stagiaires'),
            ('client', 'Client', 'Accès client pour consulter ses projets')");
        
        // Utilisateur admin par défaut
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $pdo->exec("INSERT IGNORE INTO users (name, email, password, role_id) VALUES
            ('Administrateur', '<EMAIL>', '$admin_password', 1)");
        
        // Services d'exemple
        $pdo->exec("INSERT IGNORE INTO services (nom, description, prix_base, duree_estimee, categorie, created_by) VALUES
            ('Développement Web', 'Création de sites web sur mesure', 500.00, 40, 'Développement', 1),
            ('Maintenance', 'Maintenance et support technique', 80.00, 2, 'Support', 1),
            ('Formation', 'Formation aux outils numériques', 200.00, 8, 'Formation', 1),
            ('Consultation', 'Conseil en stratégie digitale', 150.00, 4, 'Conseil', 1)");
        
        // Clients d'exemple
        $pdo->exec("INSERT IGNORE INTO clients (nom, email, telephone, entreprise, adresse, secteur_activite, status, created_by) VALUES
            ('Entreprise ABC', '<EMAIL>', '0123456789', 'ABC Corp', '123 Rue de la Paix, Paris', 'Technologie', 'actif', 1),
            ('Jean Dupont', '<EMAIL>', '0987654321', 'Freelance', '456 Avenue des Champs, Lyon', 'Consulting', 'prospect', 1),
            ('Marie Martin', '<EMAIL>', '0147258369', 'Martin SARL', '789 Boulevard Saint-Germain, Marseille', 'Commerce', 'actif', 1)");
        
        // Paramètres par défaut
        $pdo->exec("INSERT IGNORE INTO app_settings (setting_key, setting_value, setting_type, category, description) VALUES
            ('company_name', 'PioneerTech', 'string', 'company', 'Nom de l\'entreprise'),
            ('company_email', '<EMAIL>', 'string', 'company', 'Email de l\'entreprise'),
            ('default_currency', 'EUR', 'string', 'finance', 'Devise par défaut'),
            ('default_tax_rate', '20.00', 'number', 'finance', 'Taux de TVA par défaut')");
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 1.7s"><i class="fas fa-check"></i> <strong>Étape ' . $step++ . ':</strong> Données initiales ajoutées!</div>';
        flush();
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 1.9s"><i class="fas fa-shield-alt"></i> <strong>Étape ' . $step++ . ':</strong> Installation terminée!</div>';
        flush();
        
        // Résultat final
        echo '<div class="text-center mt-5 p-4 bg-light rounded">';
        echo '<h2 class="text-success mb-4"><i class="fas fa-trophy"></i> 🎉 Installation réussie! 🎉</h2>';
        
        echo '<div class="alert alert-success">';
        echo '<h5><i class="fas fa-database"></i> Base de données créée :</h5>';
        echo '<p><strong>Nom :</strong> pioneertech_v2</p>';
        echo '<p><strong>Tables :</strong> ' . $table_count . ' tables créées</p>';
        echo '<p><strong>Données :</strong> Utilisateur admin et exemples inclus</p>';
        echo '</div>';
        
        echo '<div class="alert alert-info">';
        echo '<h5><i class="fas fa-user-shield"></i> Identifiants de connexion :</h5>';
        echo '<p><strong>Email :</strong> <code><EMAIL></code></p>';
        echo '<p><strong>Mot de passe :</strong> <code>admin123</code></p>';
        echo '</div>';
        
        echo '<div class="alert alert-warning">';
        echo '<h5><i class="fas fa-exclamation-triangle"></i> Prochaines étapes :</h5>';
        echo '<ol>';
        echo '<li>Créez les fichiers de l\'application (dashboard, modules, etc.)</li>';
        echo '<li>Configurez les paramètres de l\'entreprise</li>';
        echo '<li>Changez le mot de passe administrateur</li>';
        echo '<li>Ajoutez vos propres clients et services</li>';
        echo '</ol>';
        echo '</div>';
        
        echo '<div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">';
        echo '<a href="http://localhost/phpmyadmin" target="_blank" class="btn btn-info btn-lg"><i class="fas fa-database"></i> Voir la Base de Données</a>';
        echo '</div>';
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div class="alert alert-danger mt-4"><i class="fas fa-exclamation-triangle"></i> <strong>Erreur :</strong><br><code>' . htmlspecialchars($e->getMessage()) . '</code></div>';
        
        echo '<div class="alert alert-warning">';
        echo '<h5><i class="fas fa-tools"></i> Vérifications :</h5>';
        echo '<ul>';
        echo '<li>XAMPP est-il démarré ? (Apache + MySQL)</li>';
        echo '<li>MySQL fonctionne-t-il sur le port 3306 ?</li>';
        echo '<li>L\'utilisateur root a-t-il les permissions ?</li>';
        echo '</ul>';
        echo '</div>';
        
        echo '<div class="text-center">';
        echo '<button onclick="location.reload()" class="btn btn-warning btn-lg"><i class="fas fa-redo"></i> Réessayer</button>';
        echo '</div>';
    }
    
    echo '</div>';
} else {
    // Formulaire d'installation
    echo '<div class="text-center">';
    echo '<h3 class="mb-4"><i class="fas fa-play-circle"></i> Prêt pour l\'Installation</h3>';
    echo '<p class="mb-4 text-muted">Cette installation va créer la base de données PioneerTech v2.0 avec toutes les fonctionnalités.</p>';
    
    echo '<div class="alert alert-info">';
    echo '<h5><i class="fas fa-info-circle"></i> Ce qui sera installé :</h5>';
    echo '<ul class="text-start">';
    echo '<li>✅ Base de données complète (9 tables)</li>';
    echo '<li>✅ Utilisateur administrateur</li>';
    echo '<li>✅ Données d\'exemple (clients, services)</li>';
    echo '<li>✅ Configuration de base</li>';
    echo '</ul>';
    echo '</div>';
    
    // Vérification des prérequis
    echo '<div class="alert alert-warning">';
    echo '<h5><i class="fas fa-clipboard-check"></i> Prérequis :</h5>';
    echo '<div class="row">';
    
    // Vérifier PHP
    echo '<div class="col-md-4">';
    echo '<p><i class="fas fa-check text-success"></i> PHP ' . PHP_VERSION . '</p>';
    echo '</div>';
    
    // Vérifier PDO
    echo '<div class="col-md-4">';
    if (extension_loaded('pdo') && extension_loaded('pdo_mysql')) {
        echo '<p><i class="fas fa-check text-success"></i> PDO MySQL</p>';
    } else {
        echo '<p><i class="fas fa-times text-danger"></i> PDO MySQL manquant</p>';
    }
    echo '</div>';
    
    // Vérifier MySQL
    echo '<div class="col-md-4">';
    try {
        $test_pdo = new PDO("mysql:host=localhost", "root", "");
        echo '<p><i class="fas fa-check text-success"></i> MySQL accessible</p>';
    } catch (Exception $e) {
        echo '<p><i class="fas fa-times text-danger"></i> MySQL inaccessible</p>';
    }
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    
    echo '<form method="POST" class="text-center">';
    echo '<button type="submit" name="install" class="btn btn-primary btn-lg px-5 py-3">';
    echo '<i class="fas fa-rocket"></i> Installer PioneerTech v2.0';
    echo '</button>';
    echo '</form>';
    echo '</div>';
}
?>

                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
