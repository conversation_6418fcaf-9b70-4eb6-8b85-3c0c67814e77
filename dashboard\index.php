<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once '../db/connection.php';
require_once '../db/functions.php';

$page_title = "Dashboard";
include '../templates/header.php';

// Statistiques
$stats = [
    'clients' => $pdo->query("SELECT COUNT(*) FROM clients")->fetchColumn(),
    'services' => $pdo->query("SELECT COUNT(*) FROM services")->fetchColumn(),
    'devis' => $pdo->query("SELECT COUNT(*) FROM devis")->fetchColumn(),
    'factures' => $pdo->query("SELECT COUNT(*) FROM factures")->fetchColumn()
];
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../templates/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Dashboard</h1>
            </div>

            <div class="row">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Clients</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['clients']; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Services</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['services']; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-concierge-bell fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Devis</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['devis']; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Factures</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['factures']; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-file-invoice-dollar fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Derniers clients</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            $recent_clients = $pdo->query("SELECT nom, email FROM clients ORDER BY created_at DESC LIMIT 5")->fetchAll();
                            foreach ($recent_clients as $client): ?>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="mr-3">
                                        <i class="fas fa-user text-primary"></i>
                                    </div>
                                    <div>
                                        <strong><?php echo htmlspecialchars($client['nom']); ?></strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($client['email']); ?></small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../templates/footer.php'; ?>
