<?php
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/../vendor/autoload.php'; // Pour PhpSpreadsheet

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;

class ImportExport {
    
    public static function exportClientsToExcel() {
        $clients = fetchAll("SELECT * FROM clients ORDER BY nom");
        
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // En-têtes
        $sheet->setCellValue('A1', 'ID');
        $sheet->setCellValue('B1', 'Nom');
        $sheet->setCellValue('C1', 'Email');
        $sheet->setCellValue('D1', 'Téléphone');
        $sheet->setCellValue('E1', 'Entreprise');
        $sheet->setCellValue('F1', 'Adresse');
        $sheet->setCellValue('G1', 'Date création');
        
        // Données
        $row = 2;
        foreach ($clients as $client) {
            $sheet->setCellValue('A' . $row, $client['id']);
            $sheet->setCellValue('B' . $row, $client['nom']);
            $sheet->setCellValue('C' . $row, $client['email']);
            $sheet->setCellValue('D' . $row, $client['telephone']);
            $sheet->setCellValue('E' . $row, $client['entreprise']);
            $sheet->setCellValue('F' . $row, $client['adresse']);
            $sheet->setCellValue('G' . $row, $client['date_creation']);
            $row++;
        }
        
        $writer = new Xlsx($spreadsheet);
        $filename = 'clients_' . date('Y-m-d_H-i-s') . '.xlsx';
        $filepath = UPLOAD_PATH . 'exports/' . $filename;
        
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }
        
        $writer->save($filepath);
        return $filename;
    }
    
    public static function importClientsFromExcel($filePath) {
        $spreadsheet = IOFactory::load($filePath);
        $sheet = $spreadsheet->getActiveSheet();
        $data = $sheet->toArray();
        
        $imported = 0;
        $errors = [];
        
        // Ignorer la première ligne (en-têtes)
        for ($i = 1; $i < count($data); $i++) {
            $row = $data[$i];
            
            if (empty($row[1]) || empty($row[2])) { // Nom et email requis
                $errors[] = "Ligne " . ($i + 1) . ": Nom et email requis";
                continue;
            }
            
            try {
                $sql = "INSERT INTO clients (nom, email, telephone, entreprise, adresse) VALUES (?, ?, ?, ?, ?)";
                executeQuery($sql, [
                    $row[1], // nom
                    $row[2], // email
                    $row[3], // telephone
                    $row[4], // entreprise
                    $row[5]  // adresse
                ]);
                $imported++;
            } catch (Exception $e) {
                $errors[] = "Ligne " . ($i + 1) . ": " . $e->getMessage();
            }
        }
        
        return ['imported' => $imported, 'errors' => $errors];
    }
    
    public static function generateMonthlyReport($month, $year) {
        // Rapport mensuel complet
        $data = [
            'clients_added' => self::getClientsAddedInMonth($month, $year),
            'revenue' => self::getRevenueInMonth($month, $year),
            'tasks_completed' => self::getTasksCompletedInMonth($month, $year),
            'invoices_paid' => self::getInvoicesPaidInMonth($month, $year)
        ];
        
        return self::generateReportPDF($data, $month, $year);
    }
    
    private static function getClientsAddedInMonth($month, $year) {
        $sql = "SELECT COUNT(*) as count FROM clients WHERE MONTH(date_creation) = ? AND YEAR(date_creation) = ?";
        $result = fetchOne($sql, [$month, $year]);
        return $result['count'];
    }
    
    private static function getRevenueInMonth($month, $year) {
        $sql = "SELECT SUM(montant_total) as total FROM factures WHERE MONTH(date_creation) = ? AND YEAR(date_creation) = ? AND statut = 'payee'";
        $result = fetchOne($sql, [$month, $year]);
        return $result['total'] ?? 0;
    }
    
    private static function getTasksCompletedInMonth($month, $year) {
        $sql = "SELECT COUNT(*) as count FROM taches WHERE MONTH(date_completion) = ? AND YEAR(date_completion) = ? AND statut = 'terminee'";
        $result = fetchOne($sql, [$month, $year]);
        return $result['count'];
    }
    
    private static function getInvoicesPaidInMonth($month, $year) {
        $sql = "SELECT COUNT(*) as count FROM factures WHERE MONTH(date_paiement) = ? AND YEAR(date_paiement) = ? AND statut = 'payee'";
        $result = fetchOne($sql, [$month, $year]);
        return $result['count'];
    }
    
    private static function generateReportPDF($data, $month, $year) {
        // Ici on utiliserait une librairie comme TCPDF ou DOMPDF
        // Pour l'instant, on retourne les données
        return $data;
    }
}
?>