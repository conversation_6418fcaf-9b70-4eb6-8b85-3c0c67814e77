<?php
require_once __DIR__ . '/database.php';

class NotificationSystem {
    
    public static function create($userId, $title, $message, $type = 'info', $actionUrl = null) {
        $sql = "INSERT INTO notifications (user_id, title, message, type, action_url, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
        executeQuery($sql, [$userId, $title, $message, $type, $actionUrl]);
        
        // Envoyer notification en temps réel si possible
        self::sendRealTimeNotification($userId, $title, $message, $type);
    }
    
    public static function getUnread($userId) {
        $sql = "SELECT * FROM notifications WHERE user_id = ? AND is_read = 0 ORDER BY created_at DESC";
        return fetchAll($sql, [$userId]);
    }
    
    public static function markAsRead($notificationId) {
        $sql = "UPDATE notifications SET is_read = 1, read_at = NOW() WHERE id = ?";
        executeQuery($sql, [$notificationId]);
    }
    
    public static function markAllAsRead($userId) {
        $sql = "UPDATE notifications SET is_read = 1, read_at = NOW() WHERE user_id = ? AND is_read = 0";
        executeQuery($sql, [$userId]);
    }
    
    public static function getCount($userId) {
        $sql = "SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0";
        $result = fetchOne($sql, [$userId]);
        return $result['COUNT(*)'];
    }
    
    private static function sendRealTimeNotification($userId, $title, $message, $type) {
        // Ici on peut intégrer WebSockets ou Server-Sent Events
        // Pour l'instant, on stocke juste en base
    }
    
    // Notifications automatiques pour les tâches en retard
    public static function checkOverdueTasks() {
        $sql = "
            SELECT t.*, u.id as user_id, u.name as user_name 
            FROM taches t 
            JOIN users u ON t.assigned_to = u.id 
            WHERE t.date_echeance < CURDATE() 
            AND t.statut NOT IN ('terminee', 'annulee') 
            AND t.notified_overdue = 0
        ";
        
        $overdueTasks = fetchAll($sql);
        
        foreach ($overdueTasks as $task) {
            self::create(
                $task['user_id'],
                'Tâche en retard',
                "La tâche '{$task['titre']}' est en retard depuis le " . date('d/m/Y', strtotime($task['date_echeance'])),
                'warning',
                'taches/view.php?id=' . $task['id']
            );
            
            // Marquer comme notifié
            executeQuery("UPDATE taches SET notified_overdue = 1 WHERE id = ?", [$task['id']]);
        }
    }
}
?>