<?php
// Script pour créer TOUS les fichiers PioneerTech automatiquement
// Exécutez ce script dans le dossier parent où vous voulez installer PioneerTech

$base_path = __DIR__ . '/pioneertech';

// Créer le dossier principal
if (!file_exists($base_path)) {
    mkdir($base_path, 0755, true);
}

// Liste de tous les fichiers à créer avec leur contenu de base
$files = [
    // Fichiers racine
    'index.php' => '<?php
require_once "config.php";
if (isset($_SESSION["username"])) {
    header("Location: dashboard/");
} else {
    header("Location: pages/login.php");
}
exit();
?>',

    'config.php' => '<?php
// Configuration de base de l\'application PioneerTech
define(\'DB_HOST\', \'localhost\');
define(\'DB_NAME\', \'pioneertech\');
define(\'DB_USER\', \'root\');
define(\'DB_PASS\', \'\');

// Configuration des sessions
session_start();

// Configuration des erreurs
error_reporting(E_ALL);
ini_set(\'display_errors\', 1);

// Constantes de l\'application
define(\'APP_NAME\', \'PioneerTech\');
define(\'APP_VERSION\', \'1.0.0\');
?>',

    'phpinfo.php' => '<?php phpinfo(); ?>',

    // Base de données
    'db/connection.php' => '<?php
require_once __DIR__ . \'/../config.php\';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}
?>',

    'db/functions.php' => '<?php
require_once __DIR__ . \'/connection.php\';

function getAllClients() {
    global $pdo;
    $stmt = $pdo->query("SELECT id, nom, email, telephone FROM clients ORDER BY nom");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getAllTaches() {
    global $pdo;
    $stmt = $pdo->query("SELECT * FROM taches ORDER BY date_creation DESC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function get_service_by_id($id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM services WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function get_all_services() {
    global $pdo;
    $stmt = $pdo->query("SELECT * FROM services ORDER BY nom");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>',

    // Templates
    'templates/header.php' => '<?php
require_once realpath(__DIR__ . \'/../config.php\');

if (!isset($_SESSION["username"]) && basename($_SERVER[\'PHP_SELF\']) != \'login.php\') {
    header("location: /pages/login.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php echo isset($page_title) ? $page_title . \' - \' . APP_NAME : APP_NAME; ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>',

    'templates/footer.php' => '
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>',

    'templates/sidebar.php' => '<nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="/dashboard/">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/clients/list.php">
                    <i class="fas fa-users"></i> Clients
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/services/list.php">
                    <i class="fas fa-concierge-bell"></i> Services
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/devis/list.php">
                    <i class="fas fa-file-invoice"></i> Devis
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/factures/list.php">
                    <i class="fas fa-file-invoice-dollar"></i> Factures
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/taches/list.php">
                    <i class="fas fa-tasks"></i> Tâches
                </a>
            </li>
        </ul>
    </div>
</nav>',

    // Pages d'authentification
    'pages/login.php' => '<?php
require_once \'../config.php\';

$users = [
    \'admin\' => \'admin123\'
];

if ($_SERVER[\'REQUEST_METHOD\'] === \'POST\') {
    $username = $_POST[\'username\'];
    $password = $_POST[\'password\'];

    if (isset($users[$username]) && $users[$username] === $password) {
        $_SESSION[\'username\'] = $username;
        header(\'Location: /dashboard/index.php\');
        exit;
    } else {
        $error = "Identifiants incorrects";
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Connexion - PioneerTech</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card mt-5">
                    <div class="card-header">
                        <h3 class="text-center">Connexion PioneerTech</h3>
                    </div>
                    <div class="card-body">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        <form method="post">
                            <div class="mb-3">
                                <label class="form-label">Nom d\'utilisateur</label>
                                <input type="text" name="username" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Mot de passe</label>
                                <input type="password" name="password" class="form-control" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">Se connecter</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>',

    'pages/logout.php' => '<?php
require_once \'../config.php\';
$_SESSION = array();
session_destroy();
header("location: login.php");
exit;
?>',

    // Dashboard
    'dashboard/index.php' => '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/connection.php\';
$page_title = "Dashboard";
include \'../templates/header.php\';

$stats = [
    \'clients\' => $pdo->query("SELECT COUNT(*) FROM clients")->fetchColumn(),
    \'services\' => $pdo->query("SELECT COUNT(*) FROM services")->fetchColumn(),
    \'devis\' => $pdo->query("SELECT COUNT(*) FROM devis")->fetchColumn(),
    \'factures\' => $pdo->query("SELECT COUNT(*) FROM factures")->fetchColumn()
];
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Dashboard PioneerTech</h1>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="card text-white bg-primary mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Clients</h5>
                            <p class="card-text"><?php echo $stats[\'clients\']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Services</h5>
                            <p class="card-text"><?php echo $stats[\'services\']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Devis</h5>
                            <p class="card-text"><?php echo $stats[\'devis\']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Factures</h5>
                            <p class="card-text"><?php echo $stats[\'factures\']; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>',

    // Clients
    'clients/list.php' => '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/functions.php\';
$clients = getAllClients();
$page_title = \'Clients\';
include \'../templates/header.php\';
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h3 mb-2">Liste des Clients</h1>
            
            <div class="card">
                <div class="card-header">
                    <a href="add.php" class="btn btn-primary">Ajouter un client</a>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Email</th>
                                <th>Téléphone</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($clients as $client): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($client[\'nom\']); ?></td>
                                <td><?php echo htmlspecialchars($client[\'email\']); ?></td>
                                <td><?php echo htmlspecialchars($client[\'telephone\']); ?></td>
                                <td>
                                    <a href="view.php?id=<?php echo $client[\'id\']; ?>" class="btn btn-info btn-sm">Voir</a>
                                    <a href="edit.php?id=<?php echo $client[\'id\']; ?>" class="btn btn-warning btn-sm">Modifier</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>',

    'clients/add.php' => '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/connection.php\';
$page_title = "Ajouter un client";
include \'../templates/header.php\';

if ($_SERVER[\'REQUEST_METHOD\'] === \'POST\') {
    $nom = $_POST[\'nom\'];
    $email = $_POST[\'email\'];
    $telephone = $_POST[\'telephone\'];
    $entreprise = $_POST[\'entreprise\'];
    $adresse = $_POST[\'adresse\'];

    $sql = "INSERT INTO clients (nom, email, telephone, entreprise, adresse) VALUES (?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($sql);
    
    if ($stmt->execute([$nom, $email, $telephone, $entreprise, $adresse])) {
        $success = "Client ajouté avec succès.";
    } else {
        $error = "Erreur lors de l\'ajout du client.";
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Ajouter un client</h1>
            
            <?php if (isset($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">Nom</label>
                    <input type="text" name="nom" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <input type="email" name="email" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Téléphone</label>
                    <input type="text" name="telephone" class="form-control">
                </div>
                <div class="mb-3">
                    <label class="form-label">Entreprise</label>
                    <input type="text" name="entreprise" class="form-control">
                </div>
                <div class="mb-3">
                    <label class="form-label">Adresse</label>
                    <textarea name="adresse" class="form-control" rows="3"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">Ajouter</button>
                <a href="list.php" class="btn btn-secondary">Retour</a>
            </form>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>',

    // Services
    'services/list.php' => '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/functions.php\';
$services = get_all_services();
$page_title = "Liste des services";
include \'../templates/header.php\';
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Liste des services</h1>
            
            <div class="card">
                <div class="card-header">
                    <a href="add.php" class="btn btn-primary">Ajouter un service</a>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Prix</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($services as $service): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($service[\'nom\']); ?></td>
                                <td><?php echo htmlspecialchars($service[\'prix_base\']); ?> €</td>
                                <td>
                                    <a href="edit.php?id=<?php echo $service[\'id\']; ?>" class="btn btn-warning btn-sm">Modifier</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>',

    // Fichiers de base pour les autres modules
    'devis/list.php' => '<?php echo "Module Devis - Liste"; ?>',
    'factures/list.php' => '<?php echo "Module Factures - Liste"; ?>',
    'taches/list.php' => '<?php echo "Module Tâches - Liste"; ?>',
    'contrats/list.php' => '<?php echo "Module Contrats - Liste"; ?>',
    'stagiaires/list.php' => '<?php echo "Module Stagiaires - Liste"; ?>',
    'employes/list.php' => '<?php echo "Module Employés - Liste"; ?>',
    
    // Fichiers CSS et JS
    'assets/css/style.css' => '/* Styles personnalisés PioneerTech */
body { font-family: "Nunito", sans-serif; }
.sidebar { min-height: 100vh; }
.card { box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15); }',
    
    'assets/js/scripts.js' => '// Scripts personnalisés PioneerTech
console.log("PioneerTech loaded successfully");',
];

// Créer tous les dossiers nécessaires
$folders = array_unique(array_map('dirname', array_keys($files)));
foreach ($folders as $folder) {
    if ($folder !== '.') {
        $full_path = $base_path . '/' . $folder;
        if (!file_exists($full_path)) {
            mkdir($full_path, 0755, true);
        }
    }
}

// Créer tous les fichiers
$created_count = 0;
foreach ($files as $file_path => $content) {
    $full_path = $base_path . '/' . $file_path;
    if (!file_exists($full_path)) {
        file_put_contents($full_path, $content);
        echo "✅ Créé: $file_path\n";
        $created_count++;
    } else {
        echo "⚠️  Existe déjà: $file_path\n";
    }
}

echo "\n🎉 Installation terminée!\n";
echo "📁 Dossier: $base_path\n";
echo "📄 Fichiers créés: $created_count\n";
echo "🌐 Accès: http://localhost/pioneertech/\n";
echo "👤 Login: admin / admin123\n";
?>