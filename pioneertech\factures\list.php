<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once '../db/connection.php';
$page_title = "Liste des factures";
include '../templates/header.php';

$stmt = $pdo->query("SELECT f.*, c.nom as client_nom FROM factures f LEFT JOIN clients c ON f.client_id = c.id ORDER BY f.date_creation DESC");
$factures = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../templates/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Liste des factures</h1>

            <div class="card">
                <div class="card-header">
                    <a href="add.php" class="btn btn-primary">Nouvelle facture</a>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>Client</th>
                                <th>Montant</th>
                                <th>Statut</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($factures as $facture): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($facture['numero']); ?></td>
                                <td><?php echo htmlspecialchars($facture['client_nom']); ?></td>
                                <td><?php echo number_format($facture['montant_total'], 2); ?> €</td>
                                <td>
                                    <span class="badge bg-<?php echo $facture['statut'] == 'payee' ? 'success' : 'warning'; ?>">
                                        <?php echo ucfirst($facture['statut']); ?>
                                    </span>
                                </td>
                                <td><?php echo date('d/m/Y', strtotime($facture['date_creation'])); ?></td>
                                <td>
                                    <a href="view.php?id=<?php echo $facture['id']; ?>" class="btn btn-info btn-sm">Voir</a>
                                    <a href="edit.php?id=<?php echo $facture['id']; ?>" class="btn btn-warning btn-sm">Modifier</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../templates/footer.php'; ?>