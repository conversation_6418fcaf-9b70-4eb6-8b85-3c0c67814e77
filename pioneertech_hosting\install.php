<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation PioneerTech v2.0</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-card { 
            background: white; 
            border-radius: 20px; 
            box-shadow: 0 15px 35px rgba(0,0,0,0.3); 
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            position: relative;
        }
        .step.active {
            background: #667eea;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background: #e9ecef;
            transform: translateY(-50%);
        }
        .step:last-child::after {
            display: none;
        }
        .step.completed::after {
            background: #28a745;
        }
        .progress-step {
            opacity: 0;
            animation: fadeInUp 0.6s ease forwards;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="install-card p-5">
                    <div class="text-center mb-5">
                        <h1 class="display-4 text-primary mb-3">
                            <i class="fas fa-rocket"></i> PioneerTech v2.0
                        </h1>
                        <p class="lead">Installation de l'application de gestion d'entreprise avancée</p>
                        
                        <!-- Indicateur d'étapes -->
                        <div class="step-indicator">
                            <div class="step active" id="step1">1</div>
                            <div class="step" id="step2">2</div>
                            <div class="step" id="step3">3</div>
                            <div class="step" id="step4">4</div>
                        </div>
                    </div>

<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['install'])) {
    echo '<div class="installation-progress">';
    echo '<h3 class="text-center mb-4"><i class="fas fa-cogs fa-spin"></i> Installation en cours...</h3>';
    
    $step = 1;
    
    try {
        // Configuration de la base de données
        $host = 'localhost';
        $dbname = 'pioneertech_v2';
        $username = 'root';
        $password = '';

        echo '<div class="progress-step alert alert-info" style="animation-delay: 0.1s"><i class="fas fa-info-circle"></i> <strong>Étape ' . $step++ . ':</strong> Connexion au serveur MySQL...</div>';
        flush();
        
        // Créer la connexion pour créer la base de données
        $pdo_create = new PDO("mysql:host=$host", $username, $password);
        $pdo_create->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 0.3s"><i class="fas fa-check"></i> <strong>Étape ' . $step++ . ':</strong> Connexion MySQL établie avec succès!</div>';
        flush();
        
        // Créer la base de données
        echo '<div class="progress-step alert alert-info" style="animation-delay: 0.5s"><i class="fas fa-database"></i> <strong>Étape ' . $step++ . ':</strong> Création de la base de données...</div>';
        flush();
        
        $pdo_create->exec("CREATE DATABASE IF NOT EXISTS $dbname CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 0.7s"><i class="fas fa-check-circle"></i> <strong>Étape ' . $step++ . ':</strong> Base de données "pioneertech_v2" créée avec succès!</div>';
        flush();
        
        // Se connecter à la base de données créée
        echo '<div class="progress-step alert alert-info" style="animation-delay: 0.9s"><i class="fas fa-plug"></i> <strong>Étape ' . $step++ . ':</strong> Connexion à la base de données...</div>';
        flush();
        
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo '<div class="progress-step alert alert-info" style="animation-delay: 1.1s"><i class="fas fa-table"></i> <strong>Étape ' . $step++ . ':</strong> Création des tables...</div>';
        flush();
        
        // Lire et exécuter le script SQL
        $sql_content = file_get_contents(__DIR__ . '/database/schema.sql');
        
        // Diviser le script en requêtes individuelles
        $queries = explode(';', $sql_content);
        $table_count = 0;
        $insert_count = 0;
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query) && !preg_match('/^--/', $query) && !preg_match('/^CREATE DATABASE/', $query) && !preg_match('/^USE/', $query)) {
                try {
                    $pdo->exec($query);
                    if (preg_match('/^CREATE TABLE/', $query)) {
                        $table_count++;
                    } elseif (preg_match('/^INSERT/', $query)) {
                        $insert_count++;
                    }
                } catch (Exception $e) {
                    // Ignorer les erreurs de tables existantes
                    if (!strpos($e->getMessage(), 'already exists')) {
                        throw $e;
                    }
                }
            }
        }
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 1.3s"><i class="fas fa-check-double"></i> <strong>Étape ' . $step++ . ':</strong> ' . $table_count . ' tables créées avec succès!</div>';
        flush();
        
        // Insérer les données initiales
        echo '<div class="progress-step alert alert-info" style="animation-delay: 1.5s"><i class="fas fa-users"></i> <strong>Étape ' . $step++ . ':</strong> Insertion des données initiales...</div>';
        flush();
        
        // Rôles par défaut
        $pdo->exec("INSERT IGNORE INTO roles (name, display_name, description) VALUES
            ('admin', 'Administrateur', 'Accès complet à toutes les fonctionnalités'),
            ('employee', 'Employé', 'Accès aux fonctionnalités de travail quotidien'),
            ('intern', 'Stagiaire', 'Accès limité pour les stagiaires'),
            ('client', 'Client', 'Accès client pour consulter ses projets')");
        
        // Utilisateur admin par défaut
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $pdo->exec("INSERT IGNORE INTO users (name, email, password, role_id) VALUES
            ('Administrateur', '<EMAIL>', '$admin_password', 1)");
        
        // Paramètres par défaut
        $pdo->exec("INSERT IGNORE INTO app_settings (setting_key, setting_value, setting_type, category, description) VALUES
            ('company_name', 'PioneerTech', 'string', 'company', 'Nom de l\'entreprise'),
            ('company_email', '<EMAIL>', 'string', 'company', 'Email de l\'entreprise'),
            ('default_currency', 'EUR', 'string', 'finance', 'Devise par défaut'),
            ('default_tax_rate', '20.00', 'number', 'finance', 'Taux de TVA par défaut')");
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 1.7s"><i class="fas fa-check"></i> <strong>Étape ' . $step++ . ':</strong> Données initiales ajoutées!</div>';
        flush();
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 1.9s"><i class="fas fa-shield-alt"></i> <strong>Étape ' . $step++ . ':</strong> Configuration de sécurité appliquée!</div>';
        flush();
        
        // Animation finale
        echo '<script>
        setTimeout(function() {
            document.getElementById("final-success").style.display = "block";
            document.getElementById("final-success").style.animation = "fadeInUp 0.8s ease forwards";
            
            // Mettre à jour les indicateurs d\'étapes
            document.getElementById("step1").classList.add("completed");
            document.getElementById("step2").classList.add("completed");
            document.getElementById("step3").classList.add("completed");
            document.getElementById("step4").classList.add("completed", "active");
        }, 2500);
        </script>';
        
        echo '<div id="final-success" class="text-center mt-5 p-4 bg-light rounded" style="display: none;">';
        echo '<h2 class="text-success mb-4"><i class="fas fa-trophy"></i> 🎉 Installation terminée avec succès! 🎉</h2>';
        
        echo '<div class="row mb-4">';
        echo '<div class="col-md-6">';
        echo '<div class="card border-primary h-100">';
        echo '<div class="card-header bg-primary text-white text-center"><i class="fas fa-sign-in-alt"></i> Accès à l\'application</div>';
        echo '<div class="card-body">';
        echo '<p class="mb-2"><strong>URL:</strong></p>';
        echo '<p class="mb-3"><a href="index.php" target="_blank" class="btn btn-outline-primary btn-sm">http://localhost/pioneertech_v2/</a></p>';
        echo '<p class="mb-2"><strong>Identifiants:</strong></p>';
        echo '<div class="bg-light p-2 rounded">';
        echo '<p class="mb-1"><strong>Email:</strong> <code><EMAIL></code></p>';
        echo '<p class="mb-0"><strong>Mot de passe:</strong> <code>admin123</code></p>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        
        echo '<div class="col-md-6">';
        echo '<div class="card border-info h-100">';
        echo '<div class="card-header bg-info text-white text-center"><i class="fas fa-database"></i> Informations techniques</div>';
        echo '<div class="card-body">';
        echo '<p><strong>Base de données:</strong> pioneertech_v2</p>';
        echo '<p><strong>Tables créées:</strong> ' . $table_count . '</p>';
        echo '<p><strong>Version:</strong> 2.0.0</p>';
        echo '<p><strong>Fonctionnalités:</strong> Système complet avec rôles, notifications, calendrier, support</p>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        
        echo '<div class="alert alert-warning">';
        echo '<h5><i class="fas fa-exclamation-triangle"></i> Important :</h5>';
        echo '<ul class="mb-0">';
        echo '<li>Changez le mot de passe administrateur après la première connexion</li>';
        echo '<li>Configurez les paramètres de l\'entreprise dans les réglages</li>';
        echo '<li>Supprimez ce fichier install.php après l\'installation</li>';
        echo '</ul>';
        echo '</div>';
        
        echo '<div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">';
        echo '<a href="index.php" class="btn btn-primary btn-lg me-md-2"><i class="fas fa-home"></i> Accéder à l\'application</a>';
        echo '<a href="auth/login.php" class="btn btn-success btn-lg me-md-2"><i class="fas fa-sign-in-alt"></i> Se connecter</a>';
        echo '<a href="http://localhost/phpmyadmin" target="_blank" class="btn btn-info btn-lg"><i class="fas fa-database"></i> phpMyAdmin</a>';
        echo '</div>';
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div class="alert alert-danger mt-4"><i class="fas fa-exclamation-triangle"></i> <strong>Erreur lors de l\'installation:</strong><br><code>' . htmlspecialchars($e->getMessage()) . '</code></div>';
        
        echo '<div class="alert alert-warning">';
        echo '<h5><i class="fas fa-tools"></i> Solutions possibles:</h5>';
        echo '<ol class="mb-0">';
        echo '<li><strong>Démarrez XAMPP:</strong> Assurez-vous qu\'Apache et MySQL sont démarrés</li>';
        echo '<li><strong>Vérifiez MySQL:</strong> Le service MySQL doit être en cours d\'exécution sur le port 3306</li>';
        echo '<li><strong>Permissions:</strong> Vérifiez que l\'utilisateur "root" a les permissions nécessaires</li>';
        echo '<li><strong>Redémarrage:</strong> Essayez de redémarrer les services XAMPP</li>';
        echo '</ol>';
        echo '</div>';
        
        echo '<div class="text-center">';
        echo '<button onclick="location.reload()" class="btn btn-warning btn-lg"><i class="fas fa-redo"></i> Réessayer l\'installation</button>';
        echo '</div>';
    }
    
    echo '</div>';
} else {
    // Afficher le formulaire d'installation
    echo '<div class="text-center">';
    echo '<h3 class="mb-4"><i class="fas fa-play-circle"></i> Prêt pour l\'installation</h3>';
    echo '<p class="mb-4 text-muted">Cette installation va créer automatiquement la base de données MySQL et toutes les tables nécessaires pour PioneerTech v2.0.</p>';
    
    echo '<div class="row mb-5">';
    echo '<div class="col-md-3">';
    echo '<div class="card h-100 border-primary">';
    echo '<div class="card-body text-center">';
    echo '<i class="fas fa-users-cog fa-3x text-primary mb-3"></i>';
    echo '<h5>Gestion Avancée</h5>';
    echo '<p class="small">Système de rôles et permissions complet</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="col-md-3">';
    echo '<div class="card h-100 border-success">';
    echo '<div class="card-body text-center">';
    echo '<i class="fas fa-bell fa-3x text-success mb-3"></i>';
    echo '<h5>Notifications</h5>';
    echo '<p class="small">Système de notifications en temps réel</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="col-md-3">';
    echo '<div class="card h-100 border-info">';
    echo '<div class="card-body text-center">';
    echo '<i class="fas fa-calendar-alt fa-3x text-info mb-3"></i>';
    echo '<h5>Calendrier</h5>';
    echo '<p class="small">Gestion des événements et rappels</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="col-md-3">';
    echo '<div class="card h-100 border-warning">';
    echo '<div class="card-body text-center">';
    echo '<i class="fas fa-headset fa-3x text-warning mb-3"></i>';
    echo '<h5>Support</h5>';
    echo '<p class="small">Système de tickets de support intégré</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Vérification des prérequis
    echo '<div class="alert alert-info">';
    echo '<h5><i class="fas fa-clipboard-check"></i> Vérification des prérequis:</h5>';
    echo '<div class="row">';
    
    // Vérifier PHP
    echo '<div class="col-md-4">';
    echo '<p><i class="fas fa-check text-success"></i> PHP ' . PHP_VERSION . ' ✓</p>';
    echo '</div>';
    
    // Vérifier PDO
    echo '<div class="col-md-4">';
    if (extension_loaded('pdo') && extension_loaded('pdo_mysql')) {
        echo '<p><i class="fas fa-check text-success"></i> PDO MySQL ✓</p>';
    } else {
        echo '<p><i class="fas fa-times text-danger"></i> PDO MySQL ✗</p>';
    }
    echo '</div>';
    
    // Vérifier la connexion MySQL
    echo '<div class="col-md-4">';
    try {
        $test_pdo = new PDO("mysql:host=localhost", "root", "");
        echo '<p><i class="fas fa-check text-success"></i> MySQL accessible ✓</p>';
    } catch (Exception $e) {
        echo '<p><i class="fas fa-times text-danger"></i> MySQL inaccessible ✗</p>';
    }
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    
    echo '<form method="POST" class="text-center">';
    echo '<button type="submit" name="install" class="btn btn-primary btn-lg px-5 py-3">';
    echo '<i class="fas fa-rocket"></i> Lancer l\'installation de PioneerTech v2.0';
    echo '</button>';
    echo '</form>';
    echo '</div>';
}
?>

                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
