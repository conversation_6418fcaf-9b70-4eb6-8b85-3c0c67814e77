<?php
// Script pour créer TOUS les fichiers PioneerTech automatiquement
// Exécutez ce script dans le dossier parent où vous voulez installer PioneerTech

$base_path = __DIR__ . '/pioneertech';

// Créer le dossier principal
if (!file_exists($base_path)) {
    mkdir($base_path, 0755, true);
}

// Liste de tous les fichiers à créer avec leur contenu de base
$files = [
    // Fichiers racine
    'index.php' => '<?php
require_once "config.php";
if (isset($_SESSION["username"])) {
    header("Location: dashboard/");
} else {
    header("Location: pages/login.php");
}
exit();
?>',

    'config.php' => '<?php
// Configuration de base de l\'application PioneerTech
define(\'DB_HOST\', \'localhost\');
define(\'DB_NAME\', \'pioneertech\');
define(\'DB_USER\', \'root\');
define(\'DB_PASS\', \'\');

// Configuration des sessions
session_start();

// Configuration des erreurs
error_reporting(E_ALL);
ini_set(\'display_errors\', 1);

// Constantes de l\'application
define(\'APP_NAME\', \'PioneerTech\');
define(\'APP_VERSION\', \'1.0.0\');
?>',

    'phpinfo.php' => '<?php phpinfo(); ?>',

    // Base de données
    'db/connection.php' => '<?php
require_once __DIR__ . \'/../config.php\';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}
?>',

    'db/functions.php' => '<?php
require_once __DIR__ . \'/connection.php\';

function getAllClients() {
    global $pdo;
    $stmt = $pdo->query("SELECT id, nom, email, telephone FROM clients ORDER BY nom");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getAllTaches() {
    global $pdo;
    $stmt = $pdo->query("SELECT * FROM taches ORDER BY date_creation DESC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function get_service_by_id($id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM services WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function get_all_services() {
    global $pdo;
    $stmt = $pdo->query("SELECT * FROM services ORDER BY nom");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>',

    // Templates
    'templates/header.php' => '<?php
require_once realpath(__DIR__ . \'/../config.php\');

if (!isset($_SESSION["username"]) && basename($_SERVER[\'PHP_SELF\']) != \'login.php\') {
    header("location: /pages/login.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php echo isset($page_title) ? $page_title . \' - \' . APP_NAME : APP_NAME; ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>',

    'templates/footer.php' => '
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>',

    'templates/sidebar.php' => '<nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="/dashboard/">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/clients/list.php">
                    <i class="fas fa-users"></i> Clients
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/services/list.php">
                    <i class="fas fa-concierge-bell"></i> Services
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/devis/list.php">
                    <i class="fas fa-file-invoice"></i> Devis
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/factures/list.php">
                    <i class="fas fa-file-invoice-dollar"></i> Factures
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/taches/list.php">
                    <i class="fas fa-tasks"></i> Tâches
                </a>
            </li>
        </ul>
    </div>
</nav>',

    // Pages d'authentification
    'pages/login.php' => '<?php
require_once \'../config.php\';

$users = [
    \'admin\' => \'admin123\'
];

if ($_SERVER[\'REQUEST_METHOD\'] === \'POST\') {
    $username = $_POST[\'username\'];
    $password = $_POST[\'password\'];

    if (isset($users[$username]) && $users[$username] === $password) {
        $_SESSION[\'username\'] = $username;
        header(\'Location: /dashboard/index.php\');
        exit;
    } else {
        $error = "Identifiants incorrects";
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Connexion - PioneerTech</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card mt-5">
                    <div class="card-header">
                        <h3 class="text-center">Connexion PioneerTech</h3>
                    </div>
                    <div class="card-body">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        <form method="post">
                            <div class="mb-3">
                                <label class="form-label">Nom d\'utilisateur</label>
                                <input type="text" name="username" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Mot de passe</label>
                                <input type="password" name="password" class="form-control" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">Se connecter</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>',

    'pages/logout.php' => '<?php
require_once \'../config.php\';
$_SESSION = array();
session_destroy();
header("location: login.php");
exit;
?>',

    // Dashboard
    'dashboard/index.php' => '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/connection.php\';
$page_title = "Dashboard";
include \'../templates/header.php\';

$stats = [
    \'clients\' => $pdo->query("SELECT COUNT(*) FROM clients")->fetchColumn(),
    \'services\' => $pdo->query("SELECT COUNT(*) FROM services")->fetchColumn(),
    \'devis\' => $pdo->query("SELECT COUNT(*) FROM devis")->fetchColumn(),
    \'factures\' => $pdo->query("SELECT COUNT(*) FROM factures")->fetchColumn()
];
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Dashboard PioneerTech</h1>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="card text-white bg-primary mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Clients</h5>
                            <p class="card-text"><?php echo $stats[\'clients\']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Services</h5>
                            <p class="card-text"><?php echo $stats[\'services\']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Devis</h5>
                            <p class="card-text"><?php echo $stats[\'devis\']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Factures</h5>
                            <p class="card-text"><?php echo $stats[\'factures\']; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>',

    // Clients
    'clients/list.php' => '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/functions.php\';
$clients = getAllClients();
$page_title = \'Clients\';
include \'../templates/header.php\';
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h3 mb-2">Liste des Clients</h1>
            
            <div class="card">
                <div class="card-header">
                    <a href="add.php" class="btn btn-primary">Ajouter un client</a>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Email</th>
                                <th>Téléphone</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($clients as $client): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($client[\'nom\']); ?></td>
                                <td><?php echo htmlspecialchars($client[\'email\']); ?></td>
                                <td><?php echo htmlspecialchars($client[\'telephone\']); ?></td>
                                <td>
                                    <a href="view.php?id=<?php echo $client[\'id\']; ?>" class="btn btn-info btn-sm">Voir</a>
                                    <a href="edit.php?id=<?php echo $client[\'id\']; ?>" class="btn btn-warning btn-sm">Modifier</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>',

    'clients/add.php' => '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/connection.php\';
$page_title = "Ajouter un client";
include \'../templates/header.php\';

if ($_SERVER[\'REQUEST_METHOD\'] === \'POST\') {
    $nom = $_POST[\'nom\'];
    $email = $_POST[\'email\'];
    $telephone = $_POST[\'telephone\'];
    $entreprise = $_POST[\'entreprise\'];
    $adresse = $_POST[\'adresse\'];

    $sql = "INSERT INTO clients (nom, email, telephone, entreprise, adresse) VALUES (?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($sql);
    
    if ($stmt->execute([$nom, $email, $telephone, $entreprise, $adresse])) {
        $success = "Client ajouté avec succès.";
    } else {
        $error = "Erreur lors de l\'ajout du client.";
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Ajouter un client</h1>
            
            <?php if (isset($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">Nom</label>
                    <input type="text" name="nom" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <input type="email" name="email" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Téléphone</label>
                    <input type="text" name="telephone" class="form-control">
                </div>
                <div class="mb-3">
                    <label class="form-label">Entreprise</label>
                    <input type="text" name="entreprise" class="form-control">
                </div>
                <div class="mb-3">
                    <label class="form-label">Adresse</label>
                    <textarea name="adresse" class="form-control" rows="3"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">Ajouter</button>
                <a href="list.php" class="btn btn-secondary">Retour</a>
            </form>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>',

    // Services
    'services/list.php' => '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/functions.php\';
$services = get_all_services();
$page_title = "Liste des services";
include \'../templates/header.php\';
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Liste des services</h1>
            
            <div class="card">
                <div class="card-header">
                    <a href="add.php" class="btn btn-primary">Ajouter un service</a>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Prix</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($services as $service): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($service[\'nom\']); ?></td>
                                <td><?php echo htmlspecialchars($service[\'prix_base\']); ?> €</td>
                                <td>
                                    <a href="edit.php?id=<?php echo $service[\'id\']; ?>" class="btn btn-warning btn-sm">Modifier</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>',

    // Module Devis complet
    'devis/list.php' => '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/connection.php\';
$page_title = "Liste des devis";
include \'../templates/header.php\';

$stmt = $pdo->query("SELECT d.*, c.nom as client_nom FROM devis d LEFT JOIN clients c ON d.client_id = c.id ORDER BY d.date_creation DESC");
$devis = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Liste des devis</h1>

            <div class="card">
                <div class="card-header">
                    <a href="add.php" class="btn btn-primary">Nouveau devis</a>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>Client</th>
                                <th>Montant</th>
                                <th>Statut</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($devis as $devi): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($devi[\'numero\']); ?></td>
                                <td><?php echo htmlspecialchars($devi[\'client_nom\']); ?></td>
                                <td><?php echo number_format($devi[\'montant_total\'], 2); ?> €</td>
                                <td>
                                    <span class="badge bg-<?php echo $devi[\'statut\'] == \'accepte\' ? \'success\' : ($devi[\'statut\'] == \'refuse\' ? \'danger\' : \'warning\'); ?>">
                                        <?php echo ucfirst($devi[\'statut\']); ?>
                                    </span>
                                </td>
                                <td><?php echo date(\'d/m/Y\', strtotime($devi[\'date_creation\'])); ?></td>
                                <td>
                                    <a href="view.php?id=<?php echo $devi[\'id\']; ?>" class="btn btn-info btn-sm">Voir</a>
                                    <a href="edit.php?id=<?php echo $devi[\'id\']; ?>" class="btn btn-warning btn-sm">Modifier</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>',

    'devis/add.php' => '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/connection.php\';
$page_title = "Nouveau devis";
include \'../templates/header.php\';

// Récupérer les clients
$clients_stmt = $pdo->query("SELECT id, nom FROM clients ORDER BY nom");
$clients = $clients_stmt->fetchAll(PDO::FETCH_ASSOC);

// Récupérer les services
$services_stmt = $pdo->query("SELECT id, nom, prix_base FROM services ORDER BY nom");
$services = $services_stmt->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER[\'REQUEST_METHOD\'] === \'POST\') {
    $client_id = $_POST[\'client_id\'];
    $numero = "DEV-" . date("Y") . "-" . str_pad(rand(1, 9999), 4, "0", STR_PAD_LEFT);
    $montant_total = $_POST[\'montant_total\'];
    $description = $_POST[\'description\'];

    $sql = "INSERT INTO devis (numero, client_id, montant_total, description, statut, date_creation) VALUES (?, ?, ?, ?, \'en_attente\', NOW())";
    $stmt = $pdo->prepare($sql);

    if ($stmt->execute([$numero, $client_id, $montant_total, $description])) {
        $success = "Devis créé avec succès.";
    } else {
        $error = "Erreur lors de la création du devis.";
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Nouveau devis</h1>

            <?php if (isset($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">Client</label>
                    <select name="client_id" class="form-control" required>
                        <option value="">Sélectionner un client</option>
                        <?php foreach ($clients as $client): ?>
                            <option value="<?php echo $client[\'id\']; ?>"><?php echo htmlspecialchars($client[\'nom\']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Description</label>
                    <textarea name="description" class="form-control" rows="4" required></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Montant total (€)</label>
                    <input type="number" name="montant_total" class="form-control" step="0.01" required>
                </div>
                <button type="submit" class="btn btn-primary">Créer le devis</button>
                <a href="list.php" class="btn btn-secondary">Retour</a>
            </form>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>',

    // Module Factures complet
    'factures/list.php' => '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/connection.php\';
$page_title = "Liste des factures";
include \'../templates/header.php\';

$stmt = $pdo->query("SELECT f.*, c.nom as client_nom FROM factures f LEFT JOIN clients c ON f.client_id = c.id ORDER BY f.date_creation DESC");
$factures = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Liste des factures</h1>

            <div class="card">
                <div class="card-header">
                    <a href="add.php" class="btn btn-primary">Nouvelle facture</a>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>Client</th>
                                <th>Montant</th>
                                <th>Statut</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($factures as $facture): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($facture[\'numero\']); ?></td>
                                <td><?php echo htmlspecialchars($facture[\'client_nom\']); ?></td>
                                <td><?php echo number_format($facture[\'montant_total\'], 2); ?> €</td>
                                <td>
                                    <span class="badge bg-<?php echo $facture[\'statut\'] == \'payee\' ? \'success\' : \'warning\'; ?>">
                                        <?php echo ucfirst($facture[\'statut\']); ?>
                                    </span>
                                </td>
                                <td><?php echo date(\'d/m/Y\', strtotime($facture[\'date_creation\'])); ?></td>
                                <td>
                                    <a href="view.php?id=<?php echo $facture[\'id\']; ?>" class="btn btn-info btn-sm">Voir</a>
                                    <a href="edit.php?id=<?php echo $facture[\'id\']; ?>" class="btn btn-warning btn-sm">Modifier</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>',

    // Module Tâches complet
    'taches/list.php' => '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/functions.php\';
$taches = getAllTaches();
$page_title = "Liste des tâches";
include \'../templates/header.php\';
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Liste des tâches</h1>

            <div class="card">
                <div class="card-header">
                    <a href="add.php" class="btn btn-primary">Nouvelle tâche</a>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Titre</th>
                                <th>Priorité</th>
                                <th>Statut</th>
                                <th>Date création</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($taches as $tache): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($tache[\'titre\']); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo $tache[\'priorite\'] == \'haute\' ? \'danger\' : ($tache[\'priorite\'] == \'moyenne\' ? \'warning\' : \'secondary\'); ?>">
                                        <?php echo ucfirst($tache[\'priorite\']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $tache[\'statut\'] == \'terminee\' ? \'success\' : ($tache[\'statut\'] == \'en_cours\' ? \'primary\' : \'secondary\'); ?>">
                                        <?php echo str_replace(\'_\', \' \', ucfirst($tache[\'statut\'])); ?>
                                    </span>
                                </td>
                                <td><?php echo date(\'d/m/Y\', strtotime($tache[\'date_creation\'])); ?></td>
                                <td>
                                    <a href="view.php?id=<?php echo $tache[\'id\']; ?>" class="btn btn-info btn-sm">Voir</a>
                                    <a href="edit.php?id=<?php echo $tache[\'id\']; ?>" class="btn btn-warning btn-sm">Modifier</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>',

    'taches/add.php' => '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/connection.php\';
$page_title = "Nouvelle tâche";
include \'../templates/header.php\';

if ($_SERVER[\'REQUEST_METHOD\'] === \'POST\') {
    $titre = $_POST[\'titre\'];
    $description = $_POST[\'description\'];
    $priorite = $_POST[\'priorite\'];
    $date_echeance = $_POST[\'date_echeance\'];

    $sql = "INSERT INTO taches (titre, description, priorite, statut, date_creation, date_echeance) VALUES (?, ?, ?, \'nouvelle\', NOW(), ?)";
    $stmt = $pdo->prepare($sql);

    if ($stmt->execute([$titre, $description, $priorite, $date_echeance])) {
        $success = "Tâche créée avec succès.";
    } else {
        $error = "Erreur lors de la création de la tâche.";
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Nouvelle tâche</h1>

            <?php if (isset($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">Titre</label>
                    <input type="text" name="titre" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Description</label>
                    <textarea name="description" class="form-control" rows="4"></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Priorité</label>
                    <select name="priorite" class="form-control" required>
                        <option value="basse">Basse</option>
                        <option value="moyenne" selected>Moyenne</option>
                        <option value="haute">Haute</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Date d\'échéance</label>
                    <input type="date" name="date_echeance" class="form-control">
                </div>
                <button type="submit" class="btn btn-primary">Créer la tâche</button>
                <a href="list.php" class="btn btn-secondary">Retour</a>
            </form>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>',

    // Modules supplémentaires
    'contrats/list.php' => '<?php echo "Module Contrats - Liste (à développer)"; ?>',
    'stagiaires/list.php' => '<?php echo "Module Stagiaires - Liste (à développer)"; ?>',
    'employes/list.php' => '<?php echo "Module Employés - Liste (à développer)"; ?>',

    // Fichiers CSS et JS
    'assets/css/style.css' => '/* Styles personnalisés PioneerTech */
body {
    font-family: "Nunito", sans-serif;
    background-color: #f8f9fc;
}

.sidebar {
    min-height: 100vh;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
}

.sidebar .nav-link {
    color: rgba(255,255,255,.8);
    padding: 1rem;
    border-radius: 0.35rem;
    margin: 0.25rem 0;
}

.sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255,255,255,.1);
}

.sidebar .nav-link.active {
    color: #fff;
    background-color: rgba(255,255,255,.2);
}

.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: none;
    border-radius: 0.35rem;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.btn-primary {
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(180deg, #5a6fd8 0%, #6a4190 100%);
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
}

.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

.alert {
    border: none;
    border-radius: 0.35rem;
}',

    'assets/js/scripts.js' => '// Scripts personnalisés PioneerTech
console.log("PioneerTech loaded successfully");

// Confirmation de suppression
function confirmDelete(message) {
    return confirm(message || "Êtes-vous sûr de vouloir supprimer cet élément ?");
}

// Auto-hide alerts
document.addEventListener("DOMContentLoaded", function() {
    const alerts = document.querySelectorAll(".alert");
    alerts.forEach(function(alert) {
        setTimeout(function() {
            alert.style.opacity = "0";
            setTimeout(function() {
                alert.remove();
            }, 300);
        }, 5000);
    });
});

// Sidebar active link
document.addEventListener("DOMContentLoaded", function() {
    const currentPath = window.location.pathname;
    const sidebarLinks = document.querySelectorAll(".sidebar .nav-link");

    sidebarLinks.forEach(function(link) {
        if (currentPath.includes(link.getAttribute("href"))) {
            link.classList.add("active");
        }
    });
});',
];

// Script SQL pour créer la base de données
$sql_script = '-- Script de création de la base de données PioneerTech
CREATE DATABASE IF NOT EXISTS pioneertech CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE pioneertech;

-- Table des clients
CREATE TABLE IF NOT EXISTS clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    telephone VARCHAR(20),
    entreprise VARCHAR(100),
    adresse TEXT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des services
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    description TEXT,
    prix_base DECIMAL(10,2) NOT NULL,
    duree_estimee INT, -- en heures
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des devis
CREATE TABLE IF NOT EXISTS devis (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    montant_total DECIMAL(10,2) NOT NULL,
    description TEXT,
    statut ENUM("en_attente", "accepte", "refuse") DEFAULT "en_attente",
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
);

-- Table des factures
CREATE TABLE IF NOT EXISTS factures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    devis_id INT,
    montant_total DECIMAL(10,2) NOT NULL,
    montant_paye DECIMAL(10,2) DEFAULT 0,
    statut ENUM("en_attente", "payee", "en_retard") DEFAULT "en_attente",
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_echeance DATE,
    date_paiement DATE,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (devis_id) REFERENCES devis(id) ON DELETE SET NULL
);

-- Table des tâches
CREATE TABLE IF NOT EXISTS taches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(200) NOT NULL,
    description TEXT,
    priorite ENUM("basse", "moyenne", "haute") DEFAULT "moyenne",
    statut ENUM("nouvelle", "en_cours", "terminee", "annulee") DEFAULT "nouvelle",
    assignee_id INT,
    client_id INT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_echeance DATE,
    date_completion TIMESTAMP NULL,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL
);

-- Table des contrats
CREATE TABLE IF NOT EXISTS contrats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    titre VARCHAR(200) NOT NULL,
    description TEXT,
    montant DECIMAL(10,2) NOT NULL,
    date_debut DATE NOT NULL,
    date_fin DATE NOT NULL,
    statut ENUM("actif", "expire", "resilie") DEFAULT "actif",
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
);

-- Table des employés
CREATE TABLE IF NOT EXISTS employes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    telephone VARCHAR(20),
    poste VARCHAR(100),
    salaire DECIMAL(10,2),
    date_embauche DATE,
    statut ENUM("actif", "inactif", "conge") DEFAULT "actif",
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des stagiaires
CREATE TABLE IF NOT EXISTS stagiaires (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    telephone VARCHAR(20),
    ecole VARCHAR(200),
    niveau_etude VARCHAR(100),
    date_debut DATE NOT NULL,
    date_fin DATE NOT NULL,
    tuteur_id INT,
    statut ENUM("actif", "termine", "abandonne") DEFAULT "actif",
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tuteur_id) REFERENCES employes(id) ON DELETE SET NULL
);

-- Insertion de données de test
INSERT IGNORE INTO clients (nom, email, telephone, entreprise, adresse) VALUES
("Entreprise ABC", "<EMAIL>", "0123456789", "ABC Corp", "123 Rue de la Paix, Paris"),
("Jean Dupont", "<EMAIL>", "0987654321", "Freelance", "456 Avenue des Champs, Lyon"),
("Marie Martin", "<EMAIL>", "0147258369", "Martin SARL", "789 Boulevard Saint-Germain, Marseille");

INSERT IGNORE INTO services (nom, description, prix_base, duree_estimee) VALUES
("Développement Web", "Création de sites web sur mesure", 500.00, 40),
("Maintenance", "Maintenance et support technique", 80.00, 2),
("Formation", "Formation aux outils numériques", 200.00, 8),
("Consultation", "Conseil en stratégie digitale", 150.00, 4);

INSERT IGNORE INTO taches (titre, description, priorite, statut) VALUES
("Finaliser le projet ABC", "Terminer le développement du site web pour ABC Corp", "haute", "en_cours"),
("Maintenance serveur", "Vérifier et mettre à jour les serveurs", "moyenne", "nouvelle"),
("Formation client", "Former l\'équipe de Jean Dupont aux nouveaux outils", "basse", "nouvelle");';

// Sauvegarder le script SQL
$files['db/install.sql'] = $sql_script;

// Ajouter des fichiers supplémentaires
$files['clients/view.php'] = '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/connection.php\';
$page_title = "Détails du client";
include \'../templates/header.php\';

$id = $_GET[\'id\'] ?? 0;
$stmt = $pdo->prepare("SELECT * FROM clients WHERE id = ?");
$stmt->execute([$id]);
$client = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$client) {
    header("Location: list.php");
    exit();
}
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Détails du client</h1>

            <div class="card">
                <div class="card-header">
                    <h5><?php echo htmlspecialchars($client[\'nom\']); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Email:</strong> <?php echo htmlspecialchars($client[\'email\']); ?></p>
                            <p><strong>Téléphone:</strong> <?php echo htmlspecialchars($client[\'telephone\']); ?></p>
                            <p><strong>Entreprise:</strong> <?php echo htmlspecialchars($client[\'entreprise\']); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Adresse:</strong><br><?php echo nl2br(htmlspecialchars($client[\'adresse\'])); ?></p>
                            <p><strong>Date de création:</strong> <?php echo date(\'d/m/Y H:i\', strtotime($client[\'date_creation\'])); ?></p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="edit.php?id=<?php echo $client[\'id\']; ?>" class="btn btn-warning">Modifier</a>
                        <a href="list.php" class="btn btn-secondary">Retour à la liste</a>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>';

$files['clients/edit.php'] = '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/connection.php\';
$page_title = "Modifier le client";
include \'../templates/header.php\';

$id = $_GET[\'id\'] ?? 0;
$stmt = $pdo->prepare("SELECT * FROM clients WHERE id = ?");
$stmt->execute([$id]);
$client = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$client) {
    header("Location: list.php");
    exit();
}

if ($_SERVER[\'REQUEST_METHOD\'] === \'POST\') {
    $nom = $_POST[\'nom\'];
    $email = $_POST[\'email\'];
    $telephone = $_POST[\'telephone\'];
    $entreprise = $_POST[\'entreprise\'];
    $adresse = $_POST[\'adresse\'];

    $sql = "UPDATE clients SET nom = ?, email = ?, telephone = ?, entreprise = ?, adresse = ? WHERE id = ?";
    $stmt = $pdo->prepare($sql);

    if ($stmt->execute([$nom, $email, $telephone, $entreprise, $adresse, $id])) {
        $success = "Client modifié avec succès.";
        // Recharger les données
        $stmt = $pdo->prepare("SELECT * FROM clients WHERE id = ?");
        $stmt->execute([$id]);
        $client = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        $error = "Erreur lors de la modification du client.";
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Modifier le client</h1>

            <?php if (isset($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">Nom</label>
                    <input type="text" name="nom" class="form-control" value="<?php echo htmlspecialchars($client[\'nom\']); ?>" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($client[\'email\']); ?>" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Téléphone</label>
                    <input type="text" name="telephone" class="form-control" value="<?php echo htmlspecialchars($client[\'telephone\']); ?>">
                </div>
                <div class="mb-3">
                    <label class="form-label">Entreprise</label>
                    <input type="text" name="entreprise" class="form-control" value="<?php echo htmlspecialchars($client[\'entreprise\']); ?>">
                </div>
                <div class="mb-3">
                    <label class="form-label">Adresse</label>
                    <textarea name="adresse" class="form-control" rows="3"><?php echo htmlspecialchars($client[\'adresse\']); ?></textarea>
                </div>
                <button type="submit" class="btn btn-primary">Modifier</button>
                <a href="view.php?id=<?php echo $client[\'id\']; ?>" class="btn btn-info">Voir</a>
                <a href="list.php" class="btn btn-secondary">Retour</a>
            </form>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>';

$files['services/add.php'] = '<?php
session_start();
if (!isset($_SESSION[\'username\'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once \'../db/connection.php\';
$page_title = "Ajouter un service";
include \'../templates/header.php\';

if ($_SERVER[\'REQUEST_METHOD\'] === \'POST\') {
    $nom = $_POST[\'nom\'];
    $description = $_POST[\'description\'];
    $prix_base = $_POST[\'prix_base\'];
    $duree_estimee = $_POST[\'duree_estimee\'];

    $sql = "INSERT INTO services (nom, description, prix_base, duree_estimee) VALUES (?, ?, ?, ?)";
    $stmt = $pdo->prepare($sql);

    if ($stmt->execute([$nom, $description, $prix_base, $duree_estimee])) {
        $success = "Service ajouté avec succès.";
    } else {
        $error = "Erreur lors de l\'ajout du service.";
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <?php include \'../templates/sidebar.php\'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Ajouter un service</h1>

            <?php if (isset($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">Nom du service</label>
                    <input type="text" name="nom" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Description</label>
                    <textarea name="description" class="form-control" rows="4"></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Prix de base (€)</label>
                    <input type="number" name="prix_base" class="form-control" step="0.01" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Durée estimée (heures)</label>
                    <input type="number" name="duree_estimee" class="form-control">
                </div>
                <button type="submit" class="btn btn-primary">Ajouter</button>
                <a href="list.php" class="btn btn-secondary">Retour</a>
            </form>
        </main>
    </div>
</div>

<?php include \'../templates/footer.php\'; ?>';

$files['README.md'] = '# PioneerTech - Application de Gestion

## Description
PioneerTech est une application web de gestion complète développée en PHP/MySQL pour les entreprises de services.

## Fonctionnalités
- 👥 Gestion des clients
- 🛠️ Gestion des services
- 📋 Gestion des devis
- 💰 Gestion des factures
- ✅ Gestion des tâches
- 📄 Gestion des contrats
- 👨‍💼 Gestion des employés
- 🎓 Gestion des stagiaires

## Installation

1. **Prérequis**
   - Serveur web (Apache/Nginx)
   - PHP 7.4 ou supérieur
   - MySQL 5.7 ou supérieur
   - Extension PDO PHP

2. **Installation automatique**
   ```bash
   php create_all_files.php
   ```

3. **Configuration de la base de données**
   - Importez le fichier `db/install.sql` dans votre base de données MySQL
   - Modifiez les paramètres de connexion dans `config.php`

4. **Accès à l\'application**
   - URL: `http://localhost/pioneertech/`
   - Login: `admin`
   - Mot de passe: `admin123`

## Structure du projet
```
pioneertech/
├── assets/          # CSS et JavaScript
├── clients/         # Module clients
├── dashboard/       # Tableau de bord
├── db/             # Base de données
├── devis/          # Module devis
├── factures/       # Module factures
├── pages/          # Pages d\'authentification
├── services/       # Module services
├── taches/         # Module tâches
└── templates/      # Templates communs
```

## Technologies utilisées
- **Backend**: PHP 7.4+, MySQL
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Icônes**: Font Awesome 5

## Licence
Projet développé pour PioneerTech - Tous droits réservés.
';

$files['.htaccess'] = 'RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/?$ $1/index.php [L]

# Sécurité
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

# Cache des fichiers statiques
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
</IfModule>';

$files['install.php'] = '<?php
// Script d\'installation automatique de PioneerTech
require_once "config.php";

echo "<h1>Installation de PioneerTech</h1>";

try {
    // Lire le script SQL
    $sql_content = file_get_contents(__DIR__ . \'/db/install.sql\');

    // Exécuter le script SQL
    $pdo->exec($sql_content);

    echo "<div style=\'color: green;\'>✅ Base de données installée avec succès!</div>";
    echo "<p><a href=\'index.php\'>Accéder à l\'application</a></p>";

} catch (Exception $e) {
    echo "<div style=\'color: red;\'>❌ Erreur lors de l\'installation: " . $e->getMessage() . "</div>";
}
?>';

// Créer tous les dossiers nécessaires
$folders = array_unique(array_map('dirname', array_keys($files)));
foreach ($folders as $folder) {
    if ($folder !== '.') {
        $full_path = $base_path . '/' . $folder;
        if (!file_exists($full_path)) {
            mkdir($full_path, 0755, true);
        }
    }
}

// Créer tous les fichiers
$created_count = 0;
foreach ($files as $file_path => $content) {
    $full_path = $base_path . '/' . $file_path;
    if (!file_exists($full_path)) {
        file_put_contents($full_path, $content);
        echo "✅ Créé: $file_path\n";
        $created_count++;
    } else {
        echo "⚠️  Existe déjà: $file_path\n";
    }
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 INSTALLATION PIONEERTECH TERMINÉE AVEC SUCCÈS! 🎉\n";
echo str_repeat("=", 60) . "\n\n";

echo "📊 RÉSUMÉ DE L'INSTALLATION:\n";
echo "📁 Dossier d'installation: $base_path\n";
echo "📄 Fichiers créés: $created_count\n";
echo "🗂️  Modules installés: Clients, Services, Devis, Factures, Tâches\n\n";

echo "🚀 ÉTAPES SUIVANTES:\n";
echo "1. 🗄️  Configurez votre base de données MySQL\n";
echo "2. 📝 Modifiez les paramètres dans config.php si nécessaire\n";
echo "3. 🌐 Accédez à: http://localhost/pioneertech/install.php\n";
echo "4. 🔐 Connectez-vous avec: admin / admin123\n\n";

echo "� MODULES DISPONIBLES:\n";
echo "   • Dashboard - Tableau de bord principal\n";
echo "   • Clients - Gestion complète des clients\n";
echo "   • Services - Catalogue des services\n";
echo "   • Devis - Création et suivi des devis\n";
echo "   • Factures - Gestion de la facturation\n";
echo "   • Tâches - Suivi des tâches et projets\n\n";

echo "🔧 CONFIGURATION:\n";
echo "   • Base de données: MySQL (pioneertech)\n";
echo "   • Framework CSS: Bootstrap 5\n";
echo "   • Icônes: Font Awesome 5\n";
echo "   • Authentification: Session PHP\n\n";

echo "📚 DOCUMENTATION:\n";
echo "   • README.md - Guide complet d'utilisation\n";
echo "   • db/install.sql - Script de base de données\n";
echo "   • install.php - Installation automatique de la DB\n\n";

echo "✨ PioneerTech est maintenant prêt à être utilisé!\n";
echo "💡 Conseil: Commencez par installer.php pour configurer la base de données\n";
echo str_repeat("=", 60) . "\n";
?>