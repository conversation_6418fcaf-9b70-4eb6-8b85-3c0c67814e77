<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once '../db/connection.php';
require_once '../db/functions.php';

$page_title = "Modifier le service";
include '../templates/header.php';

$service_id = $_GET['id'];
$service = get_service_by_id($service_id);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nom = $_POST['nom'];
    $details = $_POST['details'];
    $cout = $_POST['cout'];
    $duree = $_POST['duree'];

    $sql = "UPDATE services SET nom = ?, details = ?, cout = ?, duree = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssdsi", $nom, $details, $cout, $duree, $service_id);

    if ($stmt->execute()) {
        echo "<div class='alert alert-success'>Service modifié avec succès.</div>";
        $service = get_service_by_id($service_id); // Refresh data
    } else {
        echo "<div class='alert alert-danger'>Erreur lors de la modification du service: " . $conn->error . "</div>";
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../templates/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><?php echo $page_title; ?></h1>
            </div>

            <form action="edit.php?id=<?php echo $service_id; ?>" method="post">
                <div class="mb-3">
                    <label for="nom" class="form-label">Nom du service</label>
                    <input type="text" class="form-control" id="nom" name="nom" value="<?php echo htmlspecialchars($service['nom']); ?>" required>
                </div>
                <div class="mb-3">
                    <label for="details" class="form-label">Détails</label>
                    <textarea class="form-control" id="details" name="details" rows="3"><?php echo htmlspecialchars($service['details']); ?></textarea>
                </div>
                <div class="mb-3">
                    <label for="cout" class="form-label">Coût</label>
                    <input type="number" step="0.01" class="form-control" id="cout" name="cout" value="<?php echo htmlspecialchars($service['cout']); ?>" required>
                </div>
                <div class="mb-3">
                    <label for="duree" class="form-label">Durée (ex: 2 heures, 3 jours)</label>
                    <input type="text" class="form-control" id="duree" name="duree" value="<?php echo htmlspecialchars($service['duree']); ?>">
                </div>
                <button type="submit" class="btn btn-primary">Modifier</button>
            </form>
        </main>
    </div>
</div>

<?php include '../templates/footer.php'; ?>