# 🔧 Solution Simple pour l'Erreur de Base de Données

## 🎯 Problème Identifié
L'erreur `Table 'pioneertech_v2.roles' doesn't exist` indique que la base de données n'a pas été créée correctement.

## ✅ Solution Rapide via phpMyAdmin

### Étape 1 : Ouvrir phpMyAdmin
1. <PERSON><PERSON><PERSON><PERSON> XAMPP (Apache + MySQL)
2. Ouvrez votre navigateur
3. Allez sur : `http://localhost/phpmyadmin`

### Étape 2 : Créer la Base de Données
1. Cliquez sur "Nouvelle base de données" (à gauche)
2. Nom : `pioneertech_v2`
3. Interclassement : `utf8mb4_unicode_ci`
4. <PERSON><PERSON><PERSON> "Créer"

### Étape 3 : Importer le Script SQL
1. Sélectionnez la base `pioneertech_v2`
2. Cliquez sur l'onglet "SQL"
3. <PERSON><PERSON><PERSON> et collez ce script :

```sql
-- Script SQL complet pour PioneerTech v2.0

-- Table des rôles
CREATE TABLE IF NOT EXISTS roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role_id INT NOT NULL DEFAULT 1,
    avatar VARCHAR(255),
    phone VARCHAR(20),
    address TEXT,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des notifications
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    action_url VARCHAR(500),
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des clients
CREATE TABLE IF NOT EXISTS clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    telephone VARCHAR(20),
    entreprise VARCHAR(100),
    adresse TEXT,
    ville VARCHAR(100),
    code_postal VARCHAR(10),
    pays VARCHAR(100) DEFAULT 'France',
    secteur_activite VARCHAR(100),
    status ENUM('actif', 'inactif', 'prospect') DEFAULT 'prospect',
    assigned_to INT,
    created_by INT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des services
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    description TEXT,
    prix_base DECIMAL(10,2) NOT NULL,
    duree_estimee INT,
    unite_duree ENUM('heures', 'jours', 'semaines', 'mois') DEFAULT 'heures',
    categorie VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des devis
CREATE TABLE IF NOT EXISTS devis (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    montant_ht DECIMAL(10,2) NOT NULL,
    taux_tva DECIMAL(5,2) DEFAULT 20.00,
    montant_tva DECIMAL(10,2) NOT NULL,
    montant_ttc DECIMAL(10,2) NOT NULL,
    remise DECIMAL(10,2) DEFAULT 0,
    description TEXT,
    statut ENUM('brouillon', 'envoye', 'accepte', 'refuse', 'expire') DEFAULT 'brouillon',
    date_envoi TIMESTAMP NULL,
    date_acceptation TIMESTAMP NULL,
    created_by INT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des factures
CREATE TABLE IF NOT EXISTS factures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    devis_id INT,
    montant_ht DECIMAL(10,2) NOT NULL,
    taux_tva DECIMAL(5,2) DEFAULT 20.00,
    montant_tva DECIMAL(10,2) NOT NULL,
    montant_ttc DECIMAL(10,2) NOT NULL,
    montant_paye DECIMAL(10,2) DEFAULT 0,
    statut ENUM('brouillon', 'envoyee', 'payee', 'en_retard', 'annulee') DEFAULT 'brouillon',
    mode_paiement ENUM('virement', 'cheque', 'especes', 'carte', 'paypal') NULL,
    date_envoi TIMESTAMP NULL,
    date_echeance DATE,
    date_paiement TIMESTAMP NULL,
    created_by INT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des tâches
CREATE TABLE IF NOT EXISTS taches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(200) NOT NULL,
    description TEXT,
    priorite ENUM('basse', 'normale', 'haute', 'urgente') DEFAULT 'normale',
    statut ENUM('nouvelle', 'en_cours', 'en_attente', 'terminee', 'annulee') DEFAULT 'nouvelle',
    progression INT DEFAULT 0,
    client_id INT,
    assigned_to INT,
    created_by INT,
    estimated_hours DECIMAL(5,2),
    actual_hours DECIMAL(5,2) DEFAULT 0,
    date_debut DATE,
    date_echeance DATE,
    date_completion TIMESTAMP NULL,
    notified_overdue BOOLEAN DEFAULT FALSE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des paramètres
CREATE TABLE IF NOT EXISTS app_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    category VARCHAR(50) DEFAULT 'general',
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insertion des données initiales

-- Rôles par défaut
INSERT IGNORE INTO roles (name, display_name, description) VALUES
('admin', 'Administrateur', 'Accès complet à toutes les fonctionnalités'),
('employee', 'Employé', 'Accès aux fonctionnalités de travail quotidien'),
('intern', 'Stagiaire', 'Accès limité pour les stagiaires'),
('client', 'Client', 'Accès client pour consulter ses projets');

-- Utilisateur admin par défaut (mot de passe : admin123)
INSERT IGNORE INTO users (name, email, password, role_id) VALUES
('Administrateur', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1);

-- Services d'exemple
INSERT IGNORE INTO services (nom, description, prix_base, duree_estimee, categorie, created_by) VALUES
('Développement Web', 'Création de sites web sur mesure', 500.00, 40, 'Développement', 1),
('Maintenance', 'Maintenance et support technique', 80.00, 2, 'Support', 1),
('Formation', 'Formation aux outils numériques', 200.00, 8, 'Formation', 1),
('Consultation', 'Conseil en stratégie digitale', 150.00, 4, 'Conseil', 1);

-- Clients d'exemple
INSERT IGNORE INTO clients (nom, email, telephone, entreprise, adresse, secteur_activite, status, created_by) VALUES
('Entreprise ABC', '<EMAIL>', '0123456789', 'ABC Corp', '123 Rue de la Paix, Paris', 'Technologie', 'actif', 1),
('Jean Dupont', '<EMAIL>', '0987654321', 'Freelance', '456 Avenue des Champs, Lyon', 'Consulting', 'prospect', 1),
('Marie Martin', '<EMAIL>', '0147258369', 'Martin SARL', '789 Boulevard Saint-Germain, Marseille', 'Commerce', 'actif', 1);

-- Paramètres par défaut
INSERT IGNORE INTO app_settings (setting_key, setting_value, setting_type, category, description) VALUES
('company_name', 'PioneerTech', 'string', 'company', 'Nom de l\'entreprise'),
('company_email', '<EMAIL>', 'string', 'company', 'Email de l\'entreprise'),
('default_currency', 'EUR', 'string', 'finance', 'Devise par défaut'),
('default_tax_rate', '20.00', 'number', 'finance', 'Taux de TVA par défaut');
```

4. Cliquez "Exécuter"

### Étape 4 : Vérification
1. Vous devriez voir : "9 tables créées"
2. Dans la liste à gauche, vous verrez toutes les tables
3. La base de données est maintenant prête !

## 🎯 Résultat Attendu
✅ Base de données `pioneertech_v2` créée
✅ 9 tables installées
✅ Utilisateur admin configuré
✅ Données d'exemple ajoutées

## 🔐 Identifiants de Connexion
- **Email :** <EMAIL>
- **Mot de passe :** admin123

## 📱 Prochaines Étapes
1. ✅ Base de données créée manuellement
2. 🔧 Créer les fichiers de l'application
3. 🎨 Développer l'interface utilisateur
4. 🚀 Tester l'application complète

## 🆘 En Cas de Problème
- Vérifiez que XAMPP est démarré
- Assurez-vous que MySQL fonctionne
- Redémarrez Apache et MySQL si nécessaire
- Vérifiez que le port 3306 est libre

---

**Une fois la base de données créée, l'erreur sera résolue et vous pourrez développer votre application PioneerTech v2.0 !** 🎉
