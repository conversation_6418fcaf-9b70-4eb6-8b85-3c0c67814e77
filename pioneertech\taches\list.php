<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once '../db/functions.php';
$taches = getAllTaches();
$page_title = "Liste des tâches";
include '../templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../templates/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Liste des tâches</h1>

            <div class="card">
                <div class="card-header">
                    <a href="add.php" class="btn btn-primary">Nouvelle tâche</a>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Titre</th>
                                <th>Priorité</th>
                                <th>Statut</th>
                                <th>Date création</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($taches as $tache): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($tache['titre']); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo $tache['priorite'] == 'haute' ? 'danger' : ($tache['priorite'] == 'moyenne' ? 'warning' : 'secondary'); ?>">
                                        <?php echo ucfirst($tache['priorite']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $tache['statut'] == 'terminee' ? 'success' : ($tache['statut'] == 'en_cours' ? 'primary' : 'secondary'); ?>">
                                        <?php echo str_replace('_', ' ', ucfirst($tache['statut'])); ?>
                                    </span>
                                </td>
                                <td><?php echo date('d/m/Y', strtotime($tache['date_creation'])); ?></td>
                                <td>
                                    <a href="view.php?id=<?php echo $tache['id']; ?>" class="btn btn-info btn-sm">Voir</a>
                                    <a href="edit.php?id=<?php echo $tache['id']; ?>" class="btn btn-warning btn-sm">Modifier</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../templates/footer.php'; ?>