<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur QR Code - PioneerTech</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-qrcode"></i> Générateur QR Code PioneerTech</h3>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">URL de votre application :</label>
                            <input type="url" id="urlInput" class="form-control" 
                                   placeholder="https://votre-domaine.com/pioneertech_v2/"
                                   value="http://localhost/pioneertech_v2/">
                        </div>
                        
                        <button onclick="generateQR()" class="btn btn-primary">Générer QR Code</button>
                        
                        <div id="qrcode" class="text-center mt-4"></div>
                        
                        <div id="downloadSection" class="text-center mt-3" style="display: none;">
                            <button onclick="downloadQR()" class="btn btn-success">Télécharger QR Code</button>
                        </div>
                        
                        <div class="mt-4">
                            <h5>Instructions d'utilisation :</h5>
                            <ol>
                                <li>Entrez l'URL de votre application</li>
                                <li>Cliquez sur "Générer QR Code"</li>
                                <li>Téléchargez l'image</li>
                                <li>Partagez le QR Code par WhatsApp, email, etc.</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let qrCanvas;
        
        function generateQR() {
            const url = document.getElementById('urlInput').value;
            const qrContainer = document.getElementById('qrcode');
            
            // Vider le conteneur
            qrContainer.innerHTML = '';
            
            // Générer le QR Code
            QRCode.toCanvas(qrContainer, url, {
                width: 300,
                height: 300,
                colorDark: '#000000',
                colorLight: '#ffffff',
                margin: 2
            }, function (error, canvas) {
                if (error) {
                    console.error(error);
                    alert('Erreur lors de la génération du QR Code');
                } else {
                    qrCanvas = canvas;
                    document.getElementById('downloadSection').style.display = 'block';
                }
            });
        }
        
        function downloadQR() {
            if (qrCanvas) {
                const link = document.createElement('a');
                link.download = 'pioneertech_qrcode.png';
                link.href = qrCanvas.toDataURL();
                link.click();
            }
        }
        
        // Générer automatiquement au chargement
        window.onload = function() {
            generateQR();
        };
    </script>
</body>
</html>
