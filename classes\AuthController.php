<?php
class AuthController {
    public function login() {
        // Traitement du formulaire de connexion
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $username = $_POST['username'];
            $password = $_POST['password'];
            
            // Vérification des identifiants (à compléter avec la base de données)
            if ($username === 'admin' && $password === 'admin123') {
                $_SESSION['user'] = ['username' => $username, 'role' => 'admin'];
                header('Location: /');
                exit;
            } else {
                $error = 'Identifiants incorrects';
            }
        }
        
        // Affichage du formulaire de connexion
        require_once 'views/auth/login.php';
    }
    
    public function logout() {
        // Déconnexion de l'utilisateur
        session_destroy();
        header('Location: /login');
        exit;
    }
}
?>