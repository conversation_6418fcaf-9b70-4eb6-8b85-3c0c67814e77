@echo off
echo Installation de la base de données PioneerTech...
echo.

cd /d C:\xampp\mysql\bin
mysql.exe -u root -e "CREATE DATABASE IF NOT EXISTS pioneertech CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if %errorlevel% equ 0 (
    echo ✅ Base de données 'pioneertech' créée avec succès
    echo.
    echo Importation des tables...
    mysql.exe -u root pioneertech < "C:\xampp\htdocs\pioneertech\db\install.sql"
    
    if %errorlevel% equ 0 (
        echo ✅ Tables importées avec succès
        echo.
        echo 🎉 Installation terminée!
        echo 🌐 Accédez à: http://localhost/pioneertech/
        echo 👤 Login: admin
        echo 🔑 Mot de passe: admin123
    ) else (
        echo ❌ Erreur lors de l'importation des tables
    )
) else (
    echo ❌ Erreur lors de la création de la base de données
)

echo.
pause
