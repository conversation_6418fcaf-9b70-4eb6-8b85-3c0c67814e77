<?php
require_once __DIR__ . '/connection.php';

function getAllClients() {
    global $pdo;
    $stmt = $pdo->query("SELECT id, nom, email, telephone FROM clients ORDER BY nom");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getAllTaches() {
    global $pdo;
    $stmt = $pdo->query("SELECT * FROM taches ORDER BY date_creation DESC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function get_service_by_id($id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM services WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function get_all_services() {
    global $pdo;
    $stmt = $pdo->query("SELECT * FROM services ORDER BY nom");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>