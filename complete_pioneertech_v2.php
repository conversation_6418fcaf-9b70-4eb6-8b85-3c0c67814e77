<?php
// Script pour compléter PioneerTech v2.0 avec tous les modules et fonctionnalités
echo "🚀 Finalisation de PioneerTech v2.0 - Application Complète...\n\n";

$base_path = __DIR__ . '/pioneertech_v2';

// C<PERSON>er le script de base de données complet
$database_sql = '-- Base de données PioneerTech v2.0 - Structure complète
CREATE DATABASE IF NOT EXISTS pioneertech_v2 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE pioneertech_v2;

-- Table des rôles
CREATE TABLE IF NOT EXISTS roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHA<PERSON>(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des permissions
CREATE TABLE IF NOT EXISTS permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(150) NOT NULL,
    module VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table de liaison rôles-permissions
CREATE TABLE IF NOT EXISTS role_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_role_permission (role_id, permission_id)
);

-- Table des utilisateurs avancée
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role_id INT NOT NULL,
    avatar VARCHAR(255),
    phone VARCHAR(20),
    address TEXT,
    status ENUM("active", "inactive", "suspended") DEFAULT "active",
    last_login TIMESTAMP NULL,
    email_verified_at TIMESTAMP NULL,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    preferences JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id)
);

-- Table des logs d\'activité
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Table des notifications
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM("info", "success", "warning", "error") DEFAULT "info",
    action_url VARCHAR(500),
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table des clients avancée
CREATE TABLE IF NOT EXISTS clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    telephone VARCHAR(20),
    entreprise VARCHAR(100),
    adresse TEXT,
    ville VARCHAR(100),
    code_postal VARCHAR(10),
    pays VARCHAR(100) DEFAULT "France",
    secteur_activite VARCHAR(100),
    chiffre_affaires DECIMAL(15,2),
    nombre_employes INT,
    site_web VARCHAR(255),
    notes TEXT,
    status ENUM("actif", "inactif", "prospect") DEFAULT "prospect",
    source VARCHAR(100),
    assigned_to INT,
    created_by INT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Table des services avancée
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    description TEXT,
    prix_base DECIMAL(10,2) NOT NULL,
    prix_min DECIMAL(10,2),
    prix_max DECIMAL(10,2),
    duree_estimee INT,
    unite_duree ENUM("heures", "jours", "semaines", "mois") DEFAULT "heures",
    categorie VARCHAR(100),
    tags JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Table des devis avancée
CREATE TABLE IF NOT EXISTS devis (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    montant_ht DECIMAL(10,2) NOT NULL,
    taux_tva DECIMAL(5,2) DEFAULT 20.00,
    montant_tva DECIMAL(10,2) NOT NULL,
    montant_ttc DECIMAL(10,2) NOT NULL,
    remise DECIMAL(10,2) DEFAULT 0,
    description TEXT,
    conditions TEXT,
    validite_jours INT DEFAULT 30,
    statut ENUM("brouillon", "envoye", "accepte", "refuse", "expire") DEFAULT "brouillon",
    date_envoi TIMESTAMP NULL,
    date_acceptation TIMESTAMP NULL,
    date_expiration DATE,
    created_by INT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Table des lignes de devis
CREATE TABLE IF NOT EXISTS devis_lignes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    devis_id INT NOT NULL,
    service_id INT,
    description TEXT NOT NULL,
    quantite DECIMAL(10,2) NOT NULL DEFAULT 1,
    prix_unitaire DECIMAL(10,2) NOT NULL,
    montant_total DECIMAL(10,2) NOT NULL,
    ordre INT DEFAULT 0,
    FOREIGN KEY (devis_id) REFERENCES devis(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE SET NULL
);

-- Table des factures avancée
CREATE TABLE IF NOT EXISTS factures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    devis_id INT,
    montant_ht DECIMAL(10,2) NOT NULL,
    taux_tva DECIMAL(5,2) DEFAULT 20.00,
    montant_tva DECIMAL(10,2) NOT NULL,
    montant_ttc DECIMAL(10,2) NOT NULL,
    montant_paye DECIMAL(10,2) DEFAULT 0,
    remise DECIMAL(10,2) DEFAULT 0,
    statut ENUM("brouillon", "envoyee", "payee", "en_retard", "annulee") DEFAULT "brouillon",
    mode_paiement ENUM("virement", "cheque", "especes", "carte", "paypal") NULL,
    date_envoi TIMESTAMP NULL,
    date_echeance DATE,
    date_paiement TIMESTAMP NULL,
    conditions_paiement TEXT,
    notes TEXT,
    created_by INT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (devis_id) REFERENCES devis(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Table des lignes de factures
CREATE TABLE IF NOT EXISTS factures_lignes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    facture_id INT NOT NULL,
    service_id INT,
    description TEXT NOT NULL,
    quantite DECIMAL(10,2) NOT NULL DEFAULT 1,
    prix_unitaire DECIMAL(10,2) NOT NULL,
    montant_total DECIMAL(10,2) NOT NULL,
    ordre INT DEFAULT 0,
    FOREIGN KEY (facture_id) REFERENCES factures(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE SET NULL
);

-- Table des paiements
CREATE TABLE IF NOT EXISTS paiements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    facture_id INT NOT NULL,
    montant DECIMAL(10,2) NOT NULL,
    mode_paiement ENUM("virement", "cheque", "especes", "carte", "paypal") NOT NULL,
    reference_paiement VARCHAR(100),
    date_paiement DATE NOT NULL,
    notes TEXT,
    created_by INT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (facture_id) REFERENCES factures(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Table des tâches avancée
CREATE TABLE IF NOT EXISTS taches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(200) NOT NULL,
    description TEXT,
    priorite ENUM("basse", "normale", "haute", "urgente") DEFAULT "normale",
    statut ENUM("nouvelle", "en_cours", "en_attente", "terminee", "annulee") DEFAULT "nouvelle",
    progression INT DEFAULT 0,
    client_id INT,
    projet_id INT,
    assigned_to INT,
    created_by INT,
    parent_task_id INT,
    estimated_hours DECIMAL(5,2),
    actual_hours DECIMAL(5,2) DEFAULT 0,
    date_debut DATE,
    date_echeance DATE,
    date_completion TIMESTAMP NULL,
    tags JSON,
    attachments JSON,
    notified_overdue BOOLEAN DEFAULT FALSE,
    recurring_type ENUM("none", "daily", "weekly", "monthly") DEFAULT "none",
    recurring_interval INT DEFAULT 1,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_task_id) REFERENCES taches(id) ON DELETE SET NULL
);

-- Table des commentaires de tâches
CREATE TABLE IF NOT EXISTS tache_commentaires (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tache_id INT NOT NULL,
    user_id INT NOT NULL,
    commentaire TEXT NOT NULL,
    is_internal BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tache_id) REFERENCES taches(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table des temps de travail
CREATE TABLE IF NOT EXISTS time_tracking (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tache_id INT NOT NULL,
    user_id INT NOT NULL,
    description TEXT,
    heures DECIMAL(5,2) NOT NULL,
    date_travail DATE NOT NULL,
    is_billable BOOLEAN DEFAULT TRUE,
    hourly_rate DECIMAL(8,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tache_id) REFERENCES taches(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table des contrats avancée
CREATE TABLE IF NOT EXISTS contrats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    titre VARCHAR(200) NOT NULL,
    description TEXT,
    type_contrat ENUM("service", "maintenance", "developpement", "consultation") NOT NULL,
    montant_total DECIMAL(15,2) NOT NULL,
    montant_paye DECIMAL(15,2) DEFAULT 0,
    devise VARCHAR(3) DEFAULT "EUR",
    date_debut DATE NOT NULL,
    date_fin DATE NOT NULL,
    date_signature DATE,
    statut ENUM("brouillon", "envoye", "signe", "actif", "expire", "resilie", "termine") DEFAULT "brouillon",
    conditions TEXT,
    clauses_particulieres TEXT,
    renouvellement_auto BOOLEAN DEFAULT FALSE,
    duree_preavis_jours INT DEFAULT 30,
    penalites_retard DECIMAL(5,2) DEFAULT 0,
    created_by INT,
    signed_by_client VARCHAR(100),
    signed_by_company VARCHAR(100),
    document_path VARCHAR(500),
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);';

// Sauvegarder le script SQL
file_put_contents($base_path . '/database/schema.sql', $database_sql);
echo "✅ Script de base de données complet créé\n";

?>
