<?php
class Router {
    private $routes = [];

    public function addRoute($route, $controllerAction) {
        $this->routes[$route] = $controllerAction;
    }

    public function dispatch() {
        $requestUri = trim($_SERVER['REQUEST_URI'], '/');
        
        if (array_key_exists($requestUri, $this->routes)) {
            list($controller, $action) = explode('@', $this->routes[$requestUri]);
            $controllerInstance = new $controller();
            $controllerInstance->$action();
        } else {
            // Page 404
            header("HTTP/1.0 404 Not Found");
            echo 'Page non trouvée';
        }
    }
}
?>