<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation PioneerTech</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-card { 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.3); 
        }
        .step-card {
            transition: transform 0.3s ease;
        }
        .step-card:hover {
            transform: translateY(-5px);
        }
        .progress-step {
            opacity: 0;
            animation: fadeInUp 0.6s ease forwards;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-10">
                <div class="install-card p-5">
                    <div class="text-center mb-4">
                        <h1 class="display-4 text-primary mb-3">
                            <i class="fas fa-rocket"></i> PioneerTech
                        </h1>
                        <p class="lead">Installation de l'application de gestion d'entreprise</p>
                        <hr class="my-4">
                    </div>

<?php
// Script d'installation automatique de PioneerTech
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['install'])) {
    echo '<div class="installation-progress">';
    echo '<h3 class="text-center mb-4"><i class="fas fa-cogs fa-spin"></i> Installation en cours...</h3>';
    
    $step = 1;
    
    try {
        // Configuration de la base de données
        $host = 'localhost';
        $dbname = 'pioneertech';
        $username = 'root';
        $password = '';

        echo '<div class="progress-step alert alert-info" style="animation-delay: 0.1s"><i class="fas fa-info-circle"></i> <strong>Étape ' . $step++ . ':</strong> Connexion au serveur MySQL...</div>';
        flush();
        
        // Créer la connexion pour créer la base de données
        $pdo_create = new PDO("mysql:host=$host", $username, $password);
        $pdo_create->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 0.3s"><i class="fas fa-check"></i> <strong>Étape ' . $step++ . ':</strong> Connexion MySQL établie avec succès!</div>';
        flush();
        
        // Créer la base de données
        echo '<div class="progress-step alert alert-info" style="animation-delay: 0.5s"><i class="fas fa-database"></i> <strong>Étape ' . $step++ . ':</strong> Création de la base de données...</div>';
        flush();
        
        $pdo_create->exec("CREATE DATABASE IF NOT EXISTS $dbname CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 0.7s"><i class="fas fa-check-circle"></i> <strong>Étape ' . $step++ . ':</strong> Base de données "pioneertech" créée avec succès!</div>';
        flush();
        
        // Se connecter à la base de données créée
        echo '<div class="progress-step alert alert-info" style="animation-delay: 0.9s"><i class="fas fa-plug"></i> <strong>Étape ' . $step++ . ':</strong> Connexion à la base de données...</div>';
        flush();
        
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo '<div class="progress-step alert alert-info" style="animation-delay: 1.1s"><i class="fas fa-table"></i> <strong>Étape ' . $step++ . ':</strong> Création des tables...</div>';
        flush();
        
        // Script SQL intégré
        $sql_queries = [
            "CREATE TABLE IF NOT EXISTS clients (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nom VARCHAR(100) NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                telephone VARCHAR(20),
                entreprise VARCHAR(100),
                adresse TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
            
            "CREATE TABLE IF NOT EXISTS services (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nom VARCHAR(100) NOT NULL,
                description TEXT,
                prix_base DECIMAL(10,2) NOT NULL,
                duree_estimee INT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
            
            "CREATE TABLE IF NOT EXISTS devis (
                id INT AUTO_INCREMENT PRIMARY KEY,
                numero VARCHAR(50) UNIQUE NOT NULL,
                client_id INT NOT NULL,
                montant_total DECIMAL(10,2) NOT NULL,
                description TEXT,
                statut ENUM('en_attente', 'accepte', 'refuse') DEFAULT 'en_attente',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
            )",
            
            "CREATE TABLE IF NOT EXISTS factures (
                id INT AUTO_INCREMENT PRIMARY KEY,
                numero VARCHAR(50) UNIQUE NOT NULL,
                client_id INT NOT NULL,
                devis_id INT,
                montant_total DECIMAL(10,2) NOT NULL,
                montant_paye DECIMAL(10,2) DEFAULT 0,
                statut ENUM('en_attente', 'payee', 'en_retard') DEFAULT 'en_attente',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_echeance DATE,
                date_paiement DATE,
                FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
                FOREIGN KEY (devis_id) REFERENCES devis(id) ON DELETE SET NULL
            )",
            
            "CREATE TABLE IF NOT EXISTS taches (
                id INT AUTO_INCREMENT PRIMARY KEY,
                titre VARCHAR(200) NOT NULL,
                description TEXT,
                priorite ENUM('basse', 'moyenne', 'haute') DEFAULT 'moyenne',
                statut ENUM('nouvelle', 'en_cours', 'terminee', 'annulee') DEFAULT 'nouvelle',
                assignee_id INT,
                client_id INT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_echeance DATE,
                date_completion TIMESTAMP NULL,
                FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL
            )"
        ];
        
        $table_count = 0;
        foreach ($sql_queries as $query) {
            $pdo->exec($query);
            $table_count++;
        }
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 1.3s"><i class="fas fa-check-double"></i> <strong>Étape ' . $step++ . ':</strong> ' . $table_count . ' tables créées avec succès!</div>';
        flush();
        
        // Insérer des données de test
        echo '<div class="progress-step alert alert-info" style="animation-delay: 1.5s"><i class="fas fa-users"></i> <strong>Étape ' . $step++ . ':</strong> Ajout des données de test...</div>';
        flush();
        
        $insert_queries = [
            "INSERT IGNORE INTO clients (nom, email, telephone, entreprise, adresse) VALUES
                ('Entreprise ABC', '<EMAIL>', '0123456789', 'ABC Corp', '123 Rue de la Paix, Paris'),
                ('Jean Dupont', '<EMAIL>', '0987654321', 'Freelance', '456 Avenue des Champs, Lyon'),
                ('Marie Martin', '<EMAIL>', '0147258369', 'Martin SARL', '789 Boulevard Saint-Germain, Marseille')",
                
            "INSERT IGNORE INTO services (nom, description, prix_base, duree_estimee) VALUES
                ('Développement Web', 'Création de sites web sur mesure', 500.00, 40),
                ('Maintenance', 'Maintenance et support technique', 80.00, 2),
                ('Formation', 'Formation aux outils numériques', 200.00, 8),
                ('Consultation', 'Conseil en stratégie digitale', 150.00, 4)",
                
            "INSERT IGNORE INTO taches (titre, description, priorite, statut) VALUES
                ('Finaliser le projet ABC', 'Terminer le développement du site web pour ABC Corp', 'haute', 'en_cours'),
                ('Maintenance serveur', 'Vérifier et mettre à jour les serveurs', 'moyenne', 'nouvelle'),
                ('Formation client', 'Former l\\'équipe de Jean Dupont aux nouveaux outils', 'basse', 'nouvelle')"
        ];
        
        $insert_count = 0;
        foreach ($insert_queries as $query) {
            $pdo->exec($query);
            $insert_count++;
        }
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 1.7s"><i class="fas fa-check"></i> <strong>Étape ' . $step++ . ':</strong> Données de test ajoutées (' . $insert_count . ' lots)!</div>';
        flush();
        
        echo '<div class="progress-step alert alert-success" style="animation-delay: 1.9s"><i class="fas fa-shield-alt"></i> <strong>Étape ' . $step++ . ':</strong> Compte administrateur configuré!</div>';
        flush();
        
        // Animation finale
        echo '<script>
        setTimeout(function() {
            document.getElementById("final-success").style.display = "block";
            document.getElementById("final-success").style.animation = "fadeInUp 0.8s ease forwards";
        }, 2500);
        </script>';
        
        echo '<div id="final-success" class="text-center mt-5 p-4 bg-light rounded" style="display: none;">';
        echo '<h2 class="text-success mb-4"><i class="fas fa-trophy"></i> 🎉 Installation terminée avec succès! 🎉</h2>';
        
        echo '<div class="row mb-4">';
        echo '<div class="col-md-6">';
        echo '<div class="card border-primary h-100">';
        echo '<div class="card-header bg-primary text-white text-center"><i class="fas fa-sign-in-alt"></i> Accès à l\'application</div>';
        echo '<div class="card-body">';
        echo '<p class="mb-2"><strong>URL:</strong></p>';
        echo '<p class="mb-3"><a href="http://localhost/pioneertech/" target="_blank" class="btn btn-outline-primary btn-sm">http://localhost/pioneertech/</a></p>';
        echo '<p class="mb-2"><strong>Identifiants:</strong></p>';
        echo '<div class="bg-light p-2 rounded">';
        echo '<p class="mb-1"><strong>Utilisateur:</strong> <code>admin</code></p>';
        echo '<p class="mb-0"><strong>Mot de passe:</strong> <code>admin123</code></p>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        
        echo '<div class="col-md-6">';
        echo '<div class="card border-info h-100">';
        echo '<div class="card-header bg-info text-white text-center"><i class="fas fa-database"></i> Informations techniques</div>';
        echo '<div class="card-body">';
        echo '<p><strong>Base de données:</strong> pioneertech</p>';
        echo '<p><strong>Tables créées:</strong> ' . $table_count . '</p>';
        echo '<p><strong>Données de test:</strong> Incluses</p>';
        echo '<p><strong>Modules:</strong> Clients, Services, Devis, Factures, Tâches</p>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        
        echo '<div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">';
        echo '<a href="http://localhost/pioneertech/" class="btn btn-primary btn-lg me-md-2" target="_blank"><i class="fas fa-home"></i> Accéder à l\'application</a>';
        echo '<a href="http://localhost/phpmyadmin" target="_blank" class="btn btn-info btn-lg"><i class="fas fa-database"></i> phpMyAdmin</a>';
        echo '</div>';
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div class="alert alert-danger mt-4"><i class="fas fa-exclamation-triangle"></i> <strong>Erreur lors de l\'installation:</strong><br><code>' . htmlspecialchars($e->getMessage()) . '</code></div>';
        
        echo '<div class="alert alert-warning">';
        echo '<h5><i class="fas fa-tools"></i> Solutions possibles:</h5>';
        echo '<ol class="mb-0">';
        echo '<li><strong>Démarrez XAMPP:</strong> Assurez-vous qu\'Apache et MySQL sont démarrés dans le panneau de contrôle XAMPP</li>';
        echo '<li><strong>Vérifiez MySQL:</strong> Le service MySQL doit être en cours d\'exécution sur le port 3306</li>';
        echo '<li><strong>Permissions:</strong> Vérifiez que l\'utilisateur "root" a les permissions nécessaires</li>';
        echo '<li><strong>Redémarrage:</strong> Essayez de redémarrer les services XAMPP</li>';
        echo '</ol>';
        echo '</div>';
        
        echo '<div class="text-center">';
        echo '<button onclick="location.reload()" class="btn btn-warning btn-lg"><i class="fas fa-redo"></i> Réessayer l\'installation</button>';
        echo '</div>';
    }
    
    echo '</div>';
} else {
    // Afficher le formulaire d'installation
    echo '<div class="text-center">';
    echo '<h3 class="mb-4"><i class="fas fa-play-circle"></i> Prêt pour l\'installation</h3>';
    echo '<p class="mb-4 text-muted">Cette installation va créer automatiquement la base de données MySQL et toutes les tables nécessaires pour PioneerTech.</p>';
    
    echo '<div class="row mb-5">';
    echo '<div class="col-md-3">';
    echo '<div class="card step-card h-100 border-primary">';
    echo '<div class="card-body text-center">';
    echo '<i class="fas fa-database fa-3x text-primary mb-3"></i>';
    echo '<h5>Base de données</h5>';
    echo '<p class="small">Création automatique de la base MySQL "pioneertech"</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="col-md-3">';
    echo '<div class="card step-card h-100 border-success">';
    echo '<div class="card-body text-center">';
    echo '<i class="fas fa-table fa-3x text-success mb-3"></i>';
    echo '<h5>Tables</h5>';
    echo '<p class="small">Installation de toutes les tables (clients, services, devis, etc.)</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="col-md-3">';
    echo '<div class="card step-card h-100 border-info">';
    echo '<div class="card-body text-center">';
    echo '<i class="fas fa-users fa-3x text-info mb-3"></i>';
    echo '<h5>Données de test</h5>';
    echo '<p class="small">Ajout de clients, services et tâches d\'exemple</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="col-md-3">';
    echo '<div class="card step-card h-100 border-warning">';
    echo '<div class="card-body text-center">';
    echo '<i class="fas fa-user-shield fa-3x text-warning mb-3"></i>';
    echo '<h5>Compte admin</h5>';
    echo '<p class="small">Configuration du compte administrateur par défaut</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Vérification des prérequis
    echo '<div class="alert alert-info">';
    echo '<h5><i class="fas fa-clipboard-check"></i> Vérification des prérequis:</h5>';
    echo '<div class="row">';
    
    // Vérifier PHP
    echo '<div class="col-md-4">';
    echo '<p><i class="fas fa-check text-success"></i> PHP ' . PHP_VERSION . ' ✓</p>';
    echo '</div>';
    
    // Vérifier PDO
    echo '<div class="col-md-4">';
    if (extension_loaded('pdo') && extension_loaded('pdo_mysql')) {
        echo '<p><i class="fas fa-check text-success"></i> PDO MySQL ✓</p>';
    } else {
        echo '<p><i class="fas fa-times text-danger"></i> PDO MySQL ✗</p>';
    }
    echo '</div>';
    
    // Vérifier la connexion MySQL
    echo '<div class="col-md-4">';
    try {
        $test_pdo = new PDO("mysql:host=localhost", "root", "");
        echo '<p><i class="fas fa-check text-success"></i> MySQL accessible ✓</p>';
    } catch (Exception $e) {
        echo '<p><i class="fas fa-times text-danger"></i> MySQL inaccessible ✗</p>';
    }
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    
    echo '<form method="POST" class="text-center">';
    echo '<button type="submit" name="install" class="btn btn-primary btn-lg px-5 py-3">';
    echo '<i class="fas fa-rocket"></i> Lancer l\'installation';
    echo '</button>';
    echo '</form>';
    echo '</div>';
}
?>

                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
