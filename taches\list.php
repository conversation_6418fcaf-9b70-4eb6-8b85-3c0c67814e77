<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once '../db/connection.php';
require_once '../db/functions.php';

$page_title = "Liste des tâches";
include '../templates/header.php';

$taches = getAllTaches();
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../templates/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><?php echo $page_title; ?></h1>
                <a href="add.php" class="btn btn-primary">Ajouter une tâche</a>
            </div>

            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Titre</th>
                            <th>Client</th>
                            <th>Statut</th>
                            <th>Priorité</th>
                            <th>Échéance</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($taches as $tache): ?>
                        <tr>
                            <td><?php echo $tache['id']; ?></td>
                            <td><?php echo htmlspecialchars($tache['titre']); ?></td>
                            <td><?php echo htmlspecialchars($tache['client_nom'] ?? 'N/A'); ?></td>
                            <td>
                                <span class="badge bg-<?php echo $tache['statut'] == 'termine' ? 'success' : ($tache['statut'] == 'en_cours' ? 'warning' : 'secondary'); ?>">
                                    <?php echo ucfirst($tache['statut']); ?>
                                </span>
                            </td>
                            <td><?php echo ucfirst($tache['priorite']); ?></td>
                            <td><?php echo $tache['date_echeance'] ? date('d/m/Y', strtotime($tache['date_echeance'])) : 'N/A'; ?></td>
                            <td>
                                <a href="view.php?id=<?php echo $tache['id']; ?>" class="btn btn-info btn-sm">Voir</a>
                                <a href="edit.php?id=<?php echo $tache['id']; ?>" class="btn btn-warning btn-sm">Modifier</a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </main>
    </div>
</div>

<?php include '../templates/footer.php'; ?>
