Structure de dossiers pour PioneerTech:

- /frontend
  - /css
  - /js
  - /images
  - /templates

- /backend
  - /controllers
  - /models
  - /config
  - /libs

- /database
  - /migrations
  - /seeds

- /pdf
  - /templates
  - /generated

- /auth
  - /jwt
  - /sessions

- /lang
  - /fr
  - /ar

- /modules
  - /clients
  - /devis
  - /factures
  - /contrats
  - /taches
  - /stagiaires
  - /services

- /public
  - /uploads
  - /exports

- /config
- /logs