// Scripts personnalisés PioneerTech
console.log("PioneerTech loaded successfully");

// Confirmation de suppression
function confirmDelete(message) {
    return confirm(message || "Êtes-vous sûr de vouloir supprimer cet élément ?");
}

// Auto-hide alerts
document.addEventListener("DOMContentLoaded", function() {
    const alerts = document.querySelectorAll(".alert");
    alerts.forEach(function(alert) {
        setTimeout(function() {
            alert.style.opacity = "0";
            setTimeout(function() {
                alert.remove();
            }, 300);
        }, 5000);
    });
});

// Sidebar active link
document.addEventListener("DOMContentLoaded", function() {
    const currentPath = window.location.pathname;
    const sidebarLinks = document.querySelectorAll(".sidebar .nav-link");

    sidebarLinks.forEach(function(link) {
        if (currentPath.includes(link.getAttribute("href"))) {
            link.classList.add("active");
        }
    });
});