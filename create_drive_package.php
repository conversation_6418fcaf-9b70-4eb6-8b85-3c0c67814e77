<?php
// Script pour créer un package complet PioneerTech pour Google Drive
$package_path = __DIR__ . '/PioneerTech_Package';

// Créer le dossier principal du package
if (!file_exists($package_path)) {
    mkdir($package_path, 0755, true);
}

echo "🚀 Création du package PioneerTech pour Google Drive...\n\n";

// 1. Copier tous les fichiers PioneerTech
echo "📁 Copie des fichiers PioneerTech...\n";
$source_dir = __DIR__ . '/pioneertech';
$dest_dir = $package_path . '/pioneertech';

function copyDirectory($src, $dst) {
    $dir = opendir($src);
    @mkdir($dst);
    while(false !== ( $file = readdir($dir)) ) {
        if (( $file != '.' ) && ( $file != '..' )) {
            if ( is_dir($src . '/' . $file) ) {
                copyDirectory($src . '/' . $file, $dst . '/' . $file);
            }
            else {
                copy($src . '/' . $file, $dst . '/' . $file);
            }
        }
    }
    closedir($dir);
}

if (is_dir($source_dir)) {
    copyDirectory($source_dir, $dest_dir);
    echo "✅ Fichiers PioneerTech copiés\n";
} else {
    echo "⚠️ Dossier pioneertech non trouvé, création des fichiers...\n";
    // Créer les fichiers de base si le dossier n'existe pas
    mkdir($dest_dir, 0755, true);
}

// 2. Créer un fichier README détaillé
$readme_content = '# 🚀 PioneerTech - Package Complet

## 📋 Description
PioneerTech est une application web complète de gestion d\'entreprise développée en PHP/MySQL.

## 📦 Contenu du Package
- `pioneertech/` - Application complète
- `INSTALLATION.md` - Guide d\'installation détaillé
- `PARTAGE.md` - Guide de partage
- `install_auto.php` - Installation automatique
- `demo/` - Captures d\'écran et démo

## 🚀 Installation Rapide

### Méthode 1 : Installation Automatique
1. Téléchargez ce package
2. Placez le dossier `pioneertech` dans votre serveur web (htdocs pour XAMPP)
3. Ouvrez `http://localhost/pioneertech/install_auto.php`
4. Suivez les instructions

### Méthode 2 : Installation Manuelle
1. Importez `database.sql` dans phpMyAdmin
2. Configurez `config.php` avec vos paramètres de base de données
3. Accédez à `http://localhost/pioneertech/`

## 🔐 Accès par Défaut
- **Utilisateur :** admin
- **Mot de passe :** admin123

## 📱 Modules Inclus
- 👥 Gestion des clients
- 🛠️ Catalogue des services
- 📋 Création de devis
- 💰 Gestion des factures
- ✅ Suivi des tâches
- 📊 Dashboard avec statistiques

## 🌐 Partage
Ce package peut être partagé via :
- Google Drive (lien de partage)
- Email (fichier ZIP)
- WeTransfer
- Clé USB

## 📞 Support
En cas de problème :
1. Vérifiez que XAMPP est démarré
2. Consultez `INSTALLATION.md`
3. Vérifiez les prérequis techniques

## 📄 Licence
Développé pour PioneerTech - Tous droits réservés.

---
*Version 1.0 - ' . date('Y-m-d') . '*';

file_put_contents($package_path . '/README.md', $readme_content);

// 3. Créer un guide d'installation détaillé
$installation_guide = '# 📖 Guide d\'Installation PioneerTech

## 🎯 Prérequis
- Serveur web (Apache/Nginx)
- PHP 7.4 ou supérieur
- MySQL 5.7 ou supérieur
- XAMPP recommandé pour Windows

## 🚀 Installation Automatique (Recommandée)

### Étape 1 : Préparation
1. Téléchargez et décompressez le package PioneerTech
2. Démarrez XAMPP (Apache + MySQL)
3. Copiez le dossier `pioneertech` dans `C:/xampp/htdocs/`

### Étape 2 : Installation
1. Ouvrez votre navigateur
2. Allez sur `http://localhost/pioneertech/install_auto.php`
3. Cliquez sur "Lancer l\'installation"
4. Attendez la fin de l\'installation (30 secondes)

### Étape 3 : Première Connexion
1. Allez sur `http://localhost/pioneertech/`
2. Connectez-vous avec :
   - Utilisateur : `admin`
   - Mot de passe : `admin123`

## 🔧 Installation Manuelle

### Étape 1 : Base de Données
1. Ouvrez phpMyAdmin (`http://localhost/phpmyadmin`)
2. Créez une nouvelle base de données nommée `pioneertech`
3. Importez le fichier `database.sql`

### Étape 2 : Configuration
1. Ouvrez le fichier `pioneertech/config.php`
2. Modifiez les paramètres de connexion si nécessaire :
   ```php
   define(\'DB_HOST\', \'localhost\');
   define(\'DB_NAME\', \'pioneertech\');
   define(\'DB_USER\', \'root\');
   define(\'DB_PASS\', \'\');
   ```

### Étape 3 : Test
1. Accédez à `http://localhost/pioneertech/`
2. Connectez-vous avec admin/admin123

## 🐛 Résolution des Problèmes

### Erreur de Connexion à la Base de Données
- Vérifiez que MySQL est démarré dans XAMPP
- Vérifiez les paramètres dans `config.php`
- Assurez-vous que la base `pioneertech` existe

### Page Blanche
- Activez l\'affichage des erreurs PHP
- Vérifiez les logs d\'erreur Apache
- Vérifiez les permissions des fichiers

### Erreur 404
- Vérifiez que le dossier est dans `htdocs`
- Vérifiez l\'URL : `http://localhost/pioneertech/`
- Redémarrez Apache

## 📱 Utilisation Mobile
L\'application est responsive et fonctionne sur :
- Smartphones
- Tablettes  
- Ordinateurs de bureau

## 🔒 Sécurité
Après installation :
1. Changez le mot de passe admin
2. Configurez des sauvegardes régulières
3. Mettez à jour PHP/MySQL régulièrement

## 📊 Fonctionnalités Avancées
- Export PDF des devis/factures
- Notifications par email
- Rapports statistiques
- Sauvegarde automatique

---
*Support technique : Consultez README.md*';

file_put_contents($package_path . '/INSTALLATION.md', $installation_guide);

// 4. Créer un guide de partage
$partage_guide = '# 📤 Guide de Partage PioneerTech

## 🌐 Méthodes de Partage

### 1. Google Drive (Recommandé)
1. Compressez le dossier `PioneerTech_Package` en ZIP
2. Uploadez sur Google Drive
3. Clic droit → Partager → Obtenir le lien
4. Définissez les permissions : "Toute personne avec le lien"

**Lien type :** `https://drive.google.com/file/d/VOTRE_ID/view?usp=sharing`

### 2. WeTransfer
1. Allez sur wetransfer.com
2. Uploadez le fichier ZIP
3. Entrez l\'email du destinataire
4. Ajoutez un message personnalisé

### 3. Email Direct
**Sujet :** 🚀 PioneerTech - Application de Gestion d\'Entreprise

**Message type :**
```
Bonjour,

Je vous partage PioneerTech, une application de gestion d\'entreprise complète.

📦 Package complet en pièce jointe (ou lien Google Drive)

Fonctionnalités :
✅ Gestion des clients
✅ Devis et factures
✅ Suivi des projets
✅ Interface moderne

Installation :
1. Téléchargez et décompressez
2. Placez dans htdocs (XAMPP)
3. Ouvrez http://localhost/pioneertech/install_auto.php

Accès : admin / admin123

Cordialement
```

## 📱 Messages WhatsApp

### Message Court
```
🚀 PioneerTech - App de gestion d\'entreprise !
Package complet : [LIEN_GOOGLE_DRIVE]
Installation en 1 clic 👆
```

### Message Détaillé  
```
🚀 Découvrez PioneerTech !

📦 Package complet : [LIEN_GOOGLE_DRIVE]

✅ Gestion clients, devis, factures
✅ Interface moderne et responsive  
✅ Installation automatique
✅ Prêt en 2 minutes !

Instructions incluses dans le package 📖
```

## 🔗 Liens Utiles à Partager

| Type | Lien |
|------|------|
| Package Drive | `https://drive.google.com/...` |
| Démo en ligne | `http://votre-demo.com` |
| Documentation | `https://github.com/...` |

## 📋 Checklist Avant Partage

- [ ] Package testé et fonctionnel
- [ ] README.md à jour
- [ ] Guide d\'installation clair
- [ ] Captures d\'écran incluses
- [ ] Données de test présentes
- [ ] Lien de partage testé

## 🎯 Conseils de Partage

### Pour Clients
- Mettez l\'accent sur les bénéfices business
- Incluez des captures d\'écran
- Proposez une démo en direct

### Pour Développeurs
- Incluez la documentation technique
- Mentionnez les technologies utilisées
- Ajoutez les instructions de développement

### Pour Utilisateurs Finaux
- Guide d\'installation simple
- Vidéo de démonstration
- Support technique disponible

---
*Partagez PioneerTech et développez votre réseau !*';

file_put_contents($package_path . '/PARTAGE.md', $partage_guide);

// 5. Créer un installateur automatique standalone
$auto_installer = '<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation PioneerTech</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .install-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="install-card p-5">
                    <div class="text-center mb-4">
                        <h1 class="display-4 text-primary">🚀 PioneerTech</h1>
                        <p class="lead">Installation Automatique</p>
                    </div>
                    
                    <div class="alert alert-info">
                        <h5>📋 Instructions :</h5>
                        <ol>
                            <li>Assurez-vous que XAMPP est démarré (Apache + MySQL)</li>
                            <li>Placez ce dossier dans htdocs</li>
                            <li>Cliquez sur "Installer" ci-dessous</li>
                        </ol>
                    </div>
                    
                    <div class="text-center">
                        <a href="install.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-download"></i> Installer PioneerTech
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>';

file_put_contents($package_path . '/install_auto.php', $auto_installer);

// 6. Créer un fichier de démonstration
mkdir($package_path . '/demo', 0755, true);

$demo_info = '# 🎬 Démonstration PioneerTech

## 📸 Captures d\'Écran
- `login.png` - Page de connexion
- `dashboard.png` - Tableau de bord principal  
- `clients.png` - Gestion des clients
- `devis.png` - Création de devis
- `factures.png` - Gestion des factures

## 🎥 Vidéo de Démonstration
Une vidéo de démonstration est disponible montrant :
- Installation en 1 clic
- Tour des fonctionnalités
- Création d\'un client
- Génération d\'un devis
- Tableau de bord

## 🌐 Démo en Ligne
Accès à une démo en ligne : [À configurer]

## 📊 Données de Test
L\'application inclut des données de test :
- 3 clients d\'exemple
- 4 services prédéfinis  
- 3 tâches de démonstration
- Statistiques du dashboard

## 🔐 Comptes de Test
- **Admin :** admin / admin123
- **Demo :** demo / demo123 (à créer)

---
*Utilisez ces éléments pour présenter PioneerTech*';

file_put_contents($package_path . '/demo/README.md', $demo_info);

// 7. Créer un script de compression
$zip_script = '<?php
// Script pour créer un ZIP du package PioneerTech
$zip = new ZipArchive();
$zipname = "PioneerTech_v1.0_" . date("Y-m-d") . ".zip";

if ($zip->open($zipname, ZipArchive::CREATE) !== TRUE) {
    exit("Impossible de créer le fichier ZIP\n");
}

function addFolderToZip($folder, $zipFile, $exclusiveLength) {
    $handle = opendir($folder);
    while (false !== $f = readdir($handle)) {
        if ($f != "." && $f != "..") {
            $filePath = "$folder/$f";
            $localPath = substr($filePath, $exclusiveLength);
            if (is_file($filePath)) {
                $zipFile->addFile($filePath, $localPath);
            } elseif (is_dir($filePath)) {
                $zipFile->addEmptyDir($localPath);
                addFolderToZip($filePath, $zipFile, $exclusiveLength);
            }
        }
    }
    closedir($handle);
}

addFolderToZip(__DIR__, $zip, strlen(__DIR__) + 1);
$zip->close();

echo "✅ Package créé : $zipname\n";
echo "📦 Taille : " . round(filesize($zipname) / 1024 / 1024, 2) . " MB\n";
echo "🚀 Prêt pour le partage !\n";
?>';

file_put_contents($package_path . '/create_zip.php', $zip_script);

// 8. Créer un fichier de liens de partage
$share_links = '# 🔗 Liens de Partage PioneerTech

## 📤 Liens Directs (À mettre à jour)

### Google Drive
```
https://drive.google.com/file/d/VOTRE_ID_FICHIER/view?usp=sharing
```

### WeTransfer  
```
https://we.tl/VOTRE_LIEN_WETRANSFER
```

### Dropbox
```
https://www.dropbox.com/s/VOTRE_LIEN/PioneerTech.zip?dl=0
```

## 📱 Messages Prêts à Copier

### WhatsApp
```
🚀 PioneerTech - Application de gestion d\'entreprise complète !

📦 Téléchargement : [VOTRE_LIEN]

✅ Gestion clients, devis, factures
✅ Interface moderne  
✅ Installation en 1 clic
✅ Guide inclus

#GestionEntreprise #PioneerTech
```

### Email
```
Objet : 🚀 PioneerTech - Application de Gestion d\'Entreprise

Bonjour,

Je vous partage PioneerTech, une solution complète de gestion d\'entreprise.

📦 Téléchargement : [VOTRE_LIEN]

Fonctionnalités incluses :
✅ Gestion complète des clients
✅ Création de devis et factures  
✅ Suivi des projets et tâches
✅ Dashboard avec statistiques
✅ Interface responsive

Installation simple :
1. Téléchargez et décompressez
2. Placez dans htdocs (XAMPP)
3. Ouvrez install_auto.php
4. Suivez les instructions

Accès : admin / admin123

Cordialement
```

## 🎯 Instructions de Mise à Jour

1. Uploadez votre package sur Google Drive
2. Obtenez le lien de partage
3. Remplacez [VOTRE_LIEN] dans les messages
4. Testez le lien avant partage

---
*Mettez à jour ce fichier avec vos vrais liens*';

file_put_contents($package_path . '/LIENS_PARTAGE.md', $share_links);

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 PACKAGE PIONEERTECH CRÉÉ AVEC SUCCÈS ! 🎉\n";
echo str_repeat("=", 60) . "\n\n";

echo "📁 Dossier créé : $package_path\n";
echo "📄 Fichiers inclus :\n";
echo "   ✅ Application complète (pioneertech/)\n";
echo "   ✅ README.md - Documentation principale\n";
echo "   ✅ INSTALLATION.md - Guide d'installation\n";
echo "   ✅ PARTAGE.md - Guide de partage\n";
echo "   ✅ LIENS_PARTAGE.md - Messages prêts\n";
echo "   ✅ install_auto.php - Installation automatique\n";
echo "   ✅ create_zip.php - Création de ZIP\n";
echo "   ✅ demo/ - Dossier de démonstration\n\n";

echo "🚀 PROCHAINES ÉTAPES :\n";
echo "1. 📦 Compressez le dossier 'PioneerTech_Package' en ZIP\n";
echo "2. ☁️  Uploadez sur Google Drive\n";
echo "3. 🔗 Obtenez le lien de partage\n";
echo "4. 📱 Partagez via WhatsApp/Email\n\n";

echo "💡 CONSEILS :\n";
echo "   • Testez le package avant partage\n";
echo "   • Mettez à jour LIENS_PARTAGE.md avec vos vrais liens\n";
echo "   • Ajoutez des captures d'écran dans demo/\n";
echo "   • Créez une vidéo de démonstration\n\n";

echo "✨ Votre package PioneerTech est prêt pour le partage ! ✨\n";
echo str_repeat("=", 60) . "\n";
?>
