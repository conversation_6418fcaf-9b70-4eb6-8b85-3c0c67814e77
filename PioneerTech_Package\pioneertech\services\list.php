<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once '../db/functions.php';
$services = get_all_services();
$page_title = "Liste des services";
include '../templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../templates/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Liste des services</h1>
            
            <div class="card">
                <div class="card-header">
                    <a href="add.php" class="btn btn-primary">Ajouter un service</a>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Prix</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($services as $service): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($service['nom']); ?></td>
                                <td><?php echo htmlspecialchars($service['prix_base']); ?> €</td>
                                <td>
                                    <a href="edit.php?id=<?php echo $service['id']; ?>" class="btn btn-warning btn-sm">Modifier</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../templates/footer.php'; ?>