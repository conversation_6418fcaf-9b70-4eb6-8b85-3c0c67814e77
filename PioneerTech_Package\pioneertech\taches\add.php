<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once '../db/connection.php';
$page_title = "Nouvelle tâche";
include '../templates/header.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $titre = $_POST['titre'];
    $description = $_POST['description'];
    $priorite = $_POST['priorite'];
    $date_echeance = $_POST['date_echeance'];

    $sql = "INSERT INTO taches (titre, description, priorite, statut, date_creation, date_echeance) VALUES (?, ?, ?, 'nouvelle', NOW(), ?)";
    $stmt = $pdo->prepare($sql);

    if ($stmt->execute([$titre, $description, $priorite, $date_echeance])) {
        $success = "Tâche créée avec succès.";
    } else {
        $error = "Erreur lors de la création de la tâche.";
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../templates/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Nouvelle tâche</h1>

            <?php if (isset($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">Titre</label>
                    <input type="text" name="titre" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Description</label>
                    <textarea name="description" class="form-control" rows="4"></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Priorité</label>
                    <select name="priorite" class="form-control" required>
                        <option value="basse">Basse</option>
                        <option value="moyenne" selected>Moyenne</option>
                        <option value="haute">Haute</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Date d'échéance</label>
                    <input type="date" name="date_echeance" class="form-control">
                </div>
                <button type="submit" class="btn btn-primary">Créer la tâche</button>
                <a href="list.php" class="btn btn-secondary">Retour</a>
            </form>
        </main>
    </div>
</div>

<?php include '../templates/footer.php'; ?>