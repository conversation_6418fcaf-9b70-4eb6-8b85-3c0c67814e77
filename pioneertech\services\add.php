<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../pages/login.php");
    exit();
}

require_once '../db/connection.php';
$page_title = "Ajouter un service";
include '../templates/header.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nom = $_POST['nom'];
    $description = $_POST['description'];
    $prix_base = $_POST['prix_base'];
    $duree_estimee = $_POST['duree_estimee'];

    $sql = "INSERT INTO services (nom, description, prix_base, duree_estimee) VALUES (?, ?, ?, ?)";
    $stmt = $pdo->prepare($sql);

    if ($stmt->execute([$nom, $description, $prix_base, $duree_estimee])) {
        $success = "Service ajouté avec succès.";
    } else {
        $error = "Erreur lors de l'ajout du service.";
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../templates/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="h2">Ajouter un service</h1>

            <?php if (isset($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">Nom du service</label>
                    <input type="text" name="nom" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Description</label>
                    <textarea name="description" class="form-control" rows="4"></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Prix de base (€)</label>
                    <input type="number" name="prix_base" class="form-control" step="0.01" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Durée estimée (heures)</label>
                    <input type="number" name="duree_estimee" class="form-control">
                </div>
                <button type="submit" class="btn btn-primary">Ajouter</button>
                <a href="list.php" class="btn btn-secondary">Retour</a>
            </form>
        </main>
    </div>
</div>

<?php include '../templates/footer.php'; ?>