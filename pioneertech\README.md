# PioneerTech - Application de Gestion

## Description
PioneerTech est une application web de gestion complète développée en PHP/MySQL pour les entreprises de services.

## Fonctionnalités
- 👥 Gestion des clients
- 🛠️ Gestion des services
- 📋 Gestion des devis
- 💰 Gestion des factures
- ✅ Gestion des tâches
- 📄 Gestion des contrats
- 👨‍💼 Gestion des employés
- 🎓 Gestion des stagiaires

## Installation

1. **Prérequis**
   - Serveur web (Apache/Nginx)
   - PHP 7.4 ou supérieur
   - MySQL 5.7 ou supérieur
   - Extension PDO PHP

2. **Installation automatique**
   ```bash
   php create_all_files.php
   ```

3. **Configuration de la base de données**
   - Importez le fichier `db/install.sql` dans votre base de données MySQL
   - Modifiez les paramètres de connexion dans `config.php`

4. **Accès à l'application**
   - URL: `http://localhost/pioneertech/`
   - Login: `admin`
   - Mot de passe: `admin123`

## Structure du projet
```
pioneertech/
├── assets/          # CSS et JavaScript
├── clients/         # Module clients
├── dashboard/       # Tableau de bord
├── db/             # Base de données
├── devis/          # Module devis
├── factures/       # Module factures
├── pages/          # Pages d'authentification
├── services/       # Module services
├── taches/         # Module tâches
└── templates/      # Templates communs
```

## Technologies utilisées
- **Backend**: PHP 7.4+, MySQL
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Icônes**: Font Awesome 5

## Licence
Projet développé pour PioneerTech - Tous droits réservés.
